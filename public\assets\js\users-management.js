/**
 * JavaScript untuk Users Management
 * File: assets/js/users-management.js
 */

function showNotification() {
    if (sessionStorage.getItem('notificationShown') === 'true') {
        notification_success_core();
        sessionStorage.removeItem('notificationShown');
    }
}

function showNotifications() {
    if (sessionStorage.getItem('notificationShownDelete') === 'true') {
        notification_success_delete_core();
        sessionStorage.removeItem('notificationShownDelete');
    }
    if (sessionStorage.getItem('notificationShownUpdate') === 'true') {
        notification_success_update_core();
        sessionStorage.removeItem('notificationShownUpdate');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const csrfName = csrf_token;
    const csrfHash = $('input[name="' + csrf_token + '"]').val();
    
    // Handle active/inactive status changes for edit modal
    $('#edit_is_active').change(function() {
        const isActive = $(this).val();
        if (isActive === '0') { // Jika status tidak aktif
            $('#inactive_reason_group').show();
            $('#inactive_date_group').show();
        } else { // Jika status aktif
            $('#inactive_reason_group').hide();
            $('#inactive_date_group').hide();
            $('#mutasi_group').hide();
        }
    });

    // Handle edit user modal show event
    $('#editUserModal').on('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        $('#editUserId').val(button.dataset.userId);
        $('#edit_nama').val(button.dataset.nama);
        $('#edit_email').val(button.dataset.email);
        $('#edit_nip').val(button.dataset.nip);
        $('#edit_role').val(button.dataset.roleId);
        $('#edit_is_active').val(button.dataset.isActive);
        $('#edit_unit_kerja').val(button.dataset.unitKerja);
        $('#edit_mutasi').val(button.dataset.mutasi);
        $('#edit_inactive_reason').val(button.dataset.inactiveReason);
        $('#edit_inactive_date').val(button.dataset.inactiveDate);
        $('#edit_department').val(button.dataset.department);
        $('#edit_division').val(button.dataset.division);
        $('#edit_microsoft_id').val(button.dataset.microsoftId);
        
        $('#edit_is_active').change(); // Trigger change untuk menampilkan grup yang sesuai
        $('#edit_inactive_reason').change(); // Trigger change untuk menampilkan mutasi jika perlu
    });

    // Handle inactive reason changes for edit modal
    $('#edit_inactive_reason').change(function() {
        const reason = $(this).val();
        if (reason === 'Mutasi') {
            $('#mutasi_group').show();
        } else {
            $('#mutasi_group').hide();
        }
    });

    // Handle active/inactive status changes for add modal
    $('#is_active').change(function() {
        const isActive = $(this).val();
        if (isActive === '0') { // Jika status tidak aktif
            $('#inactive_reason_group').show();
            $('#inactive_date_group').show();
        } else { // Jika status aktif
            $('#inactive_reason_group').hide();
            $('#inactive_date_group').hide();
            $('#mutasi_group').hide();
        }
    });

    // Handle inactive reason changes for add modal
    $('#inactive_reason').change(function() {
        const reason = $(this).val();
        if (reason === 'Mutasi') {
            $('#mutasi_group').show();
        } else {
            $('#mutasi_group').hide();
        }
    });

    // Handle add user form submission
    $('#userForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: base_url + 'administrator/addUser',
            type: 'POST',
            data: $(this).serialize() + '&' + csrfName + '=' + csrfHash,
            dataType: 'json',
            success: function(response) {
                if(response.status === 'success') {
                    sessionStorage.setItem('notificationShown', 'true');
                    $('#addUsersModal').modal('hide');
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('Terjadi kesalahan saat menambah pengguna.');
                $('#addUsersModal').modal('hide');
            }
        });
    });

    // Handle delete user modal show event
    $('#DeleteUserModal').on('show.bs.modal', function(event) {
        $(this).find('#deleteUserId').val(event.relatedTarget.dataset.userId);
    });

    // Handle delete user form submission
    $('#deleteUserForm').on('submit', function(e) {
        e.preventDefault();

        // Ambil CSRF token terbaru
        const currentCsrfHash = $('input[name="' + csrf_token + '"]').val();
        const formData = $(this).serialize() + '&' + csrfName + '=' + currentCsrfHash;

        $.ajax({
            url: base_url + 'administrator/deleteUser',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if(response.status === 'success') {
                    sessionStorage.setItem('notificationShownDelete', 'true');
                    $('#DeleteUserModal').modal('hide');
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Delete user error:', error);
                console.error('Response:', xhr.responseText);
                alert('Terjadi kesalahan saat menghapus pengguna.');
                $('#DeleteUserModal').modal('hide');
            }
        });
    });

    // Handle edit user form submission
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: base_url + 'administrator/updateUser',
            type: 'POST',
            data: $(this).serialize() + '&' + csrfName + '=' + csrfHash,
            dataType: 'json',
            success: function(response) {
                if(response.status === 'success') {
                    sessionStorage.setItem('notificationShownUpdate', 'true');
                    $('#editUserModal').modal('hide');
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('Terjadi kesalahan saat memperbarui pengguna.');
                $('#editUserModal').modal('hide');
            }
        });
    });

    // Show notifications
    showNotification();
    showNotifications();
});
