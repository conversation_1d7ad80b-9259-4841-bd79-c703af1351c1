<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\BudgetingModel;
use App\Models\UserModel;
use App\Models\ThemeCoreModel;
use App\Models\RoleModel;
use App\Models\MenuModel;
use App\Models\AccessMenuModel;
use App\Models\UnitModel;
use App\Models\DepartmentModel;
use App\Models\DivisionModel;
use App\Models\JabatanModel;
use App\Models\BankModel;
use App\Models\UniversitasModel;
use App\Models\JurusanUniversitasModel;
use App\Models\RkapModel;
use App\Models\RkapNonBudgetModel;
use App\Models\RealisasiModel;
use CodeIgniter\Log\Logger;
use App\Helpers\code_helper;
use App\Models\LpjbModel;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use App\Models\SignatureModel;
use DateTime;
use App\Models\SalaryBudgetModel;
use App\Models\SignatureSalaryModel;
use App\Models\VendorModel;
use App\Models\SignatureLpjbModel;

class BudgetingController extends Controller
{
    protected $userModel;
    protected $roleModel;
    protected $menuModel;
    protected $accessMenuModel;
    protected $unitModel;
    protected $themeCoreModel;
    protected $departmentModel;
    protected $divisionModel;
    protected $jabatanModel;
    protected $bankModel;
    protected $universitasModel;
    protected $jurusanUniversitasModel;
    protected $rkapModel;
    protected $budgetingModel;
    protected $rkapNonBudgetModel;
    protected $realisasiModel;
    protected $lpjbModel;
    protected $signatureModel;
    protected $salaryBudgetModel;
    protected $signatureSalaryModel;
    protected $signatureLpjbModel;
    public function __construct()
    {
        $this->bankModel = new BankModel();
        $this->budgetingModel = new BudgetingModel();
        $this->rkapModel = new RkapModel();
        $this->rkapNonBudgetModel = new RkapNonBudgetModel();
        $this->realisasiModel = new RealisasiModel();
        $this->lpjbModel = new LpjbModel();
        $this->signatureModel = new SignatureModel();
        $this->salaryBudgetModel = new SalaryBudgetModel();
        $this->signatureSalaryModel = new SignatureSalaryModel();
        $this->signatureLpjbModel = new SignatureLpjbModel();
        // Memuat helper jika diperlukan
        helper(['url', 'form', 'code']);
    }

    // public function index()
    // {
    //     $data['title'] = 'Dashboard';
    //     // $data['users'] = $this->userModel->findAll();

    //     return view('budgeting/partials/header')
    //         . view('budgeting/partials/sidebar', $data)
    //         . view('budgeting/partials/topbar', $data)
    //         . view('budgeting/index', $data)
    //         . view('budgeting/partials/footer');
    // }

    public function rkap()
    {
        $data['title'] = 'RKAP Tahunan';

        // Mendapatkan unit dan department dari session
        $unit = session()->get('unit_kerja');
        $department = session()->get('department');

        // Mengambil accNoOptions dari BudgetingModel
        $accNoOptions = $this->budgetingModel->findAllAccNosWithDescription();

        // Mengambil parameter tahun dari query string
        $year = $this->request->getGet('year') ?? date('Y'); // Default ke tahun saat ini jika tidak dipilih

        // Mengambil tahun dari RealisasiModel berdasarkan field 'tanggal'
        $realisasiYears = $this->realisasiModel->select('YEAR(tanggal) as year')
            ->distinct()
            ->orderBy('year', 'DESC')
            ->findAll();

        // Mengambil tahun dari RkapModel berdasarkan field 'year'
        $rkapYears = $this->rkapModel->select('year')
            ->distinct()
            ->orderBy('year', 'DESC')
            ->findAll();

        // Menggabungkan kedua sumber tahun
        $combinedYears = array_merge(
            array_map(function ($item) {
                return $item['year'];
            }, $realisasiYears),
            array_map(function ($item) {
                return $item['year'];
            }, $rkapYears)
        );

        // Menghapus duplikasi dan mengurutkan tahun secara menurun
        $uniqueYears = array_unique($combinedYears);
        rsort($uniqueYears);

        $data['available_years'] = $uniqueYears;

        // Mengambil data RKAP tanpa melakukan SUM dan GROUP BY
        $rkaps = $this->rkapModel->select('
            id,
            acc_no,
            description_sec,
            description,
            total,
            year
        ')->where('year', $year)
            ->where('unit', $unit)
            ->where('department', $department)
            ->orderBy('acc_no', 'ASC') // Menambahkan pengurutan berdasarkan acc_no
            ->findAll();

        // Cek realisasi untuk setiap RKAP
        $realisasiModel = new RealisasiModel();
        foreach ($rkaps as &$rkap) {
            $rkap['has_realisasi'] = $realisasiModel->where('master_id', $rkap['id'])->countAllResults() > 0;
        }

        $data['rkaps'] = $rkaps;

        // Menyiapkan realisasi per entri RKAP berdasarkan 'id'
        $realisasiMap = [];
        if (!empty($rkaps)) {
            // Mengambil daftar id unik dari RKAP untuk digunakan di realisasi
            $rkapIds = array_column($rkaps, 'id');

            // Mengambil data realisasi yang sesuai dengan RKAP id dan jenis_realisasi
            $realisasiData = $this->realisasiModel->select('master_id, jenis_realisasi, SUM(actual) as total_actual')
                ->whereIn('master_id', $rkapIds)
                ->where([
                    'unit' => $unit,
                    'department' => $department,
                    'YEAR(tanggal)' => $year
                ])
                ->groupBy(['master_id', 'jenis_realisasi'])
                ->findAll();

            // Mengelompokkan data realisasi berdasarkan master_id dan jenis_realisasi
            foreach ($realisasiData as $realisasi) {
                $master_id = $realisasi['master_id'];
                $jenis_realisasi = $realisasi['jenis_realisasi'];
                $actual = floatval($realisasi['total_actual']);

                if (!isset($realisasiMap[$master_id])) {
                    $realisasiMap[$master_id] = [
                        'RKAP BUDGETTER' => 0,
                        'RKAP NON BUDGETTER' => 0
                    ];
                }

                if ($jenis_realisasi === 'RKAP BUDGETTER') {
                    $realisasiMap[$master_id]['RKAP BUDGETTER'] = $actual;
                } elseif ($jenis_realisasi === 'RKAP NON BUDGETTER') {
                    $realisasiMap[$master_id]['RKAP NON BUDGETTER'] = $actual;
                }
            }
        }

        $data['realisasiMap'] = $realisasiMap;
        $data['selected_year'] = $year;

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/rkap', $data)
            . view('budgeting/partials/footer');
    }

    public function masterData()
    {
        $data['title'] = 'Master Data';
        $data['budgeting'] = $this->budgetingModel->findAll();
        // $data['acc_primary_options'] = $this->budgetingModel->getAllAccPrimary();
        // $data['acc_secondary_options'] = $this->budgetingModel->getAllAccSecondary();

        return view('budgeting/partials/header', $data)
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/master-data', $data)
            . view('budgeting/partials/footer');
    }

    public function store()
    {
        if ($this->request->isAJAX()) {
            $acc_no = $this->request->getPost('acc_no');
            $acc_primary = $this->request->getPost('acc_primary');
            $acc_secondary = $this->request->getPost('acc_secondary');
            $description = $this->request->getPost('description_acc');
            $description_secondary = $this->request->getPost('description_secondary');
            $created_at = date('Y-m-d H:i:s');

            $data = [
                'acc_no'               => $acc_no,
                'acc_primary'          => $acc_primary,
                'acc_secondary'        => $acc_secondary,
                'description_acc'      => $description,
                'description_secondary' => $description_secondary,
                'created_at'           => $created_at,
            ];

            // Lakukan validasi
            if (!$this->budgetingModel->insert($data)) {
                if ($this->budgetingModel->errors()) {
                    $errors = $this->budgetingModel->errors();
                    return $this->response->setJSON([
                        'status' => 'error',
                        'errors' => $errors
                    ]);
                }
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Data berhasil ditambahkan.'
            ]);
        }

        // Jika bukan AJAX, redirect biasa
        return redirect()->to('/budget/masterData')->with('error', 'Permintaan tidak valid.');
    }

    public function getBudgetingData()
    {
        $data = $this->budgetingModel->findAll();
        return $this->response->setJSON($data);
    }

    public function update($id)
    {
        if ($this->request->isAJAX()) {
            $acc_no = $this->request->getPost('acc_no');
            $acc_primary = $this->request->getPost('acc_primary');
            $acc_secondary = $this->request->getPost('acc_secondary');
            $description = $this->request->getPost('description_acc');
            $description_secondary = $this->request->getPost('description_secondary');

            $data = [
                'acc_no'               => $acc_no,
                'acc_primary'          => $acc_primary,
                'acc_secondary'        => $acc_secondary,
                'description_acc'      => $description,
                'description_secondary' => $description_secondary,
            ];

            if (!$this->budgetingModel->update($id, $data)) {
                if ($this->budgetingModel->errors()) {
                    $errors = $this->budgetingModel->errors();
                    return $this->response->setJSON([
                        'status' => 'error',
                        'errors' => $errors
                    ]);
                }
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Data berhasil diperbarui.'
            ]);
        }

        return redirect()->to('/budget/masterData')->with('error', 'Permintaan tidak valid.');
    }

    public function delete($id)
    {
        if ($this->request->isAJAX()) {
            if ($this->budgetingModel->delete($id)) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Data berhasil dihapus.'
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => 'error',
                    'errors' => ['db' => 'Gagal menghapus data dari database.']
                ]);
            }
        }

        return $this->response->setJSON(['error' => 'Permintaan tidak valid.'], 400);
    }

    public function get($id)
    {
        $data = $this->budgetingModel->find($id);
        if ($data) {
            return $this->response->setJSON($data);
        } else {
            return $this->response->setJSON(['error' => 'Data tidak ditemukan.'], 404);
        }
    }

    /**
     * Menampilkan form RKAP dengan pilihan dinamis
     */
    public function formRkap()
    {
        // Mendapatkan unit dan department dari session
        $session = session();
        $unit = $session->get('unit_kerja');
        $department = $session->get('department');

        // Pastikan unit dan department tersedia
        if (empty($unit) || empty($department)) {
            // Redirect ke halaman yang sesuai atau tampilkan pesan error
            return redirect()->to('/dashboard')->with('error', 'Unit kerja atau departemen belum ditetapkan.');
        }

        // Mengambil semua acc_primary dari BudgetingModel
        $accPrimaryOptions = $this->budgetingModel->getAllAccPrimary();

        // Mengambil accNoOptions dari BudgetingModel
        $accNoOptions = $this->budgetingModel->findAllAccNosWithDescription();

        // Mengambil daftar tahun yang tersedia dari RKAP
        $available_years = $this->rkapModel->select('year')->distinct()->orderBy('year', 'DESC')->findAll();

        // Ekstrak tahun ke dalam array
        $years = array_map(function ($item) {
            return $item['year'];
        }, $available_years);

        $data = [
            'title' => 'Form RKAP',
            'accPrimaryOptions' => $accPrimaryOptions,
            'accNoOptions' => $accNoOptions,
            'available_years' => $years,
        ];

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/form-rkap', $data)
            . view('budgeting/partials/footer');
    }

    public function getDescriptionByAccNo()
    {
        $data = $this->request->getJSON();
        if (!$data || !isset($data->acc_no)) {
            return $this->respond(['error' => 'Invalid input'], 400);
        }

        $accNo = $data->acc_no;
        $description = $this->model->getDescription($accNo);

        return $this->respond(['description' => $description]);
    }

    public function storeRkap()
    {
        log_message('info', 'storeRkap: Mulai proses penyimpanan RKAP.');

        if ($this->request->isAJAX()) {
            $rkapModel = new RkapModel();

            // Ambil data dari form
            $acc_no = $this->request->getPost('acc_no');
            $description = $this->request->getPost('description');
            $year = $this->request->getPost('year');

            // Tambahkan data filter tambahan
            $acc_primary = $this->request->getPost('acc_primary');
            $acc_secondary = $this->request->getPost('acc_secondary');

            $jan = $this->request->getPost('jan');
            $feb = $this->request->getPost('feb');
            $mar = $this->request->getPost('mar');
            $apr = $this->request->getPost('apr');
            $mei = $this->request->getPost('mei');
            $jun = $this->request->getPost('jun');
            $jul = $this->request->getPost('jul');
            $agu = $this->request->getPost('agu');
            $sep = $this->request->getPost('sep');
            $okt = $this->request->getPost('okt');
            $nov = $this->request->getPost('nov');
            $des = $this->request->getPost('des');
            $total = $this->request->getPost('total');

            log_message('info', 'storeRkap: Data diterima dari form.');

            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            // Hapus data existing berdasarkan tahun, unit, dan departemen
            $rkapModel->where(['year' => $year, 'unit' => $unit, 'department' => $department])->delete();

            log_message('info', 'storeRkap: Data lama dihapus sebelum menyimpan data baru.');

            // Iterate melalui setiap entri RKAP dan simpan
            foreach ($acc_no as $index => $value) {
                $data = [
                    'acc_no'      => $acc_no[$index],
                    'description' => $description[$index],
                    'year'        => $year,
                    'acc_primary' => $acc_primary[$index], // Pastikan field ini ada
                    'acc_secondary' => $acc_secondary[$index], // Pastikan field ini ada
                    'jan'         => $jan[$index],
                    'feb'         => $feb[$index],
                    'mar'         => $mar[$index],
                    'apr'         => $apr[$index],
                    'mei'         => $mei[$index],
                    'jun'         => $jun[$index],
                    'jul'         => $jul[$index],
                    'agu'         => $agu[$index],
                    'sep'         => $sep[$index],
                    'okt'         => $okt[$index],
                    'nov'         => $nov[$index],
                    'des'         => $des[$index],
                    'total'       => $total[$index],
                    'unit'        => $unit,
                    'department'  => $department
                ];

                // Validasi dan simpan data
                if (!$rkapModel->insert($data)) {
                    if ($rkapModel->errors()) {
                        $errors = $rkapModel->errors();
                        return $this->response->setJSON([
                            'status' => 'error',
                            'errors' => $errors
                        ]);
                    }
                }
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Data RKAP berhasil disimpan.'
            ]);
        }

        // Jika bukan AJAX, redirect biasa
        return redirect()->to('/budget/formRkap')->with('error', 'Permintaan tidak valid.');
    }

    public function storeProposal()
    {
        if ($this->request->isAJAX()) {
            $rkapNonBudgetModel = new RkapNonBudgetModel();

            // Ambil data dari form
            $acc_no = $this->request->getPost('acc_no');
            $description = $this->request->getPost('description');
            // $schedule = $this->request->getPost('schedule');
            $jan = $this->request->getPost('jan');
            $feb = $this->request->getPost('feb');
            $mar = $this->request->getPost('mar');
            $apr = $this->request->getPost('apr');
            $mei = $this->request->getPost('mei');
            $jun = $this->request->getPost('jun');
            $jul = $this->request->getPost('jul');
            $agu = $this->request->getPost('agu');
            $sep = $this->request->getPost('sep');
            $okt = $this->request->getPost('okt');
            $nov = $this->request->getPost('nov');
            $des = $this->request->getPost('des');
            $year = $this->request->getPost('year');

            // Validasi jumlah item
            if (count($acc_no) !== count($description) || count($acc_no) !== count($schedule)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Jumlah item tidak konsisten.'
                ]);
            }

            // Ambil unit dan department dari session
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            // Hapus data existing berdasarkan tahun, unit, dan departemen
            $rkapNonBudgetModel->where(['year' => $year, 'unit' => $unit, 'department' => $department])->delete();

            log_message('info', 'storeProposal: Data lama dihapus sebelum menyimpan data baru.');

            // Inisialisasi array untuk menyimpan data yang akan dimasukkan
            $dataToInsert = [];

            foreach ($acc_no as $index => $value) {
                $data = [
                    'acc_no'      => $acc_no[$index],
                    'description' => $description[$index],
                    // 'schedule'    => $schedule[$index],
                    'jan'         => isset($jan[$index]) ? 'Yes' : '',
                    'feb'         => isset($feb[$index]) ? 'Yes' : '',
                    'mar'         => isset($mar[$index]) ? 'Yes' : '',
                    'apr'         => isset($apr[$index]) ? 'Yes' : '',
                    'mei'         => isset($mei[$index]) ? 'Yes' : '',
                    'jun'         => isset($jun[$index]) ? 'Yes' : '',
                    'jul'         => isset($jul[$index]) ? 'Yes' : '',
                    'agu'         => isset($agu[$index]) ? 'Yes' : '',
                    'sep'         => isset($sep[$index]) ? 'Yes' : '',
                    'okt'         => isset($okt[$index]) ? 'Yes' : '',
                    'nov'         => isset($nov[$index]) ? 'Yes' : '',
                    'des'         => isset($des[$index]) ? 'Yes' : '',
                    'total'       => '0', // Karena tidak ada perhitungan
                    'year'        => $year,
                    'unit'        => $unit,
                    'department'  => $department,
                    'created_at'  => date('Y-m-d H:i:s'),
                    'updated_at'  => date('Y-m-d H:i:s'),
                ];

                // Tambahkan data ke array
                $dataToInsert[] = $data;
            }

            // Validasi setiap entri
            foreach ($dataToInsert as $data) {
                if (!$rkapNonBudgetModel->validate($data)) {
                    // Ambil pesan kesalahan
                    $errors = $rkapNonBudgetModel->errors();

                    return $this->response->setJSON([
                        'status' => 'error',
                        'errors' => $errors
                    ]);
                }
            }

            // Simpan semua data sekaligus
            if ($rkapNonBudgetModel->insertBatch($dataToInsert)) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Data berhasil disimpan.'
                ]);
            } else {
                // Jika penyimpanan gagal, kirim pesan kesalahan
                $errors = $rkapNonBudgetModel->errors();
                return $this->response->setJSON([
                    'status' => 'error',
                    'errors' => ['db' => 'Gagal menyimpan data ke database.']
                ]);
            }
        }

        // Jika bukan AJAX, kembalikan error
        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid.'
        ], 400);
    }

    public function getDataByYear()
    {
        if ($this->request->isAJAX()) {
            $year = $this->request->getPost('year');
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            // Mengambil data RKAP Budget
            $rkaps = $this->rkapModel->where(['year' => $year, 'unit' => $unit, 'department' => $department])->findAll();

            // Mengambil data Program Kerja Tahunan Tanpa Biaya
            $proposals = $this->rkapNonBudgetModel->where(['year' => $year, 'unit' => $unit, 'department' => $department])->findAll();

            return $this->response->setJSON([
                'status' => 'success',
                'rkaps' => $rkaps,
                'proposals' => $proposals
            ]);
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid.'
        ], 400);
    }

    public function realisasi()
    {
        // Mendapatkan tipe form dari parameter GET atau default ke 'PP'
        $type = $this->request->getGet('type') ?: 'PP';
        $allowedTypes = ['PP', 'PUM'];
        if (!in_array($type, $allowedTypes)) {
            $type = 'PP';
        }

        // Mendapatkan data unit dan departemen dari session
        $session = session();
        $unit = $session->get('unit_kerja');
        $department = $session->get('department');
        $currentYear = date('Y');
        $currentMonth = date('n'); // Tambahkan ini untuk mendapatkan bulan saat ini (1-12)

        // Pastikan unit dan departemen tersedia
        if (empty($unit) || empty($department)) {
            return redirect()->to('/dashboard')->with('error', 'Unit kerja atau departemen belum ditetapkan.');
        }

        // Mengambil acc_no dari RKAP yang sudah diinput
        $accNoOptions = $this->rkapModel->getRkapsByYearUnitDepartments(
            $currentYear,
            $unit,
            $department
        );




        $data['title'] = 'Realisasi';
        // Menghasilkan nomor form
        $formNumber = generate_form_number($department, $unit, $type);
        $data['form_number'] = $formNumber;
        $data['accNoOptions'] = $accNoOptions;
        $data['selectedType'] = $type;

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/realisasi', $data)
            . view('budgeting/partials/footer');
    }

    public function getTotalBudget()
    {
        if ($this->request->isAJAX()) {
            try {
                // Ambil data JSON dari request
                $request = $this->request->getJSON();

                $acc_id = $request->acc_id ?? null;
                $master_id = $request->master_id ?? null;
                $jenis_realisasi = $request->realisasi ?? null;
                $unit = session()->get('unit_kerja');
                $department = session()->get('department');

                // Log input parameters
                log_message('debug', 'getTotalBudget parameters: ' . json_encode([
                    'acc_id' => $acc_id,
                    'master_id' => $master_id,
                    'jenis_realisasi' => $jenis_realisasi,
                    'unit' => $unit,
                    'department' => $department
                ]));

                // Cek realisasi terakhir
                $lastDifference = $this->realisasiModel->getLatestDifferenceByAccNos(
                    $acc_id,
                    $department,
                    $unit,
                    $jenis_realisasi
                );
                if ($jenis_realisasi == 'RKAP NON BUDGETTER') {
                    // Jika ada realisasi sebelumnya, gunakan nilai different
                    if ($lastDifference !== null) {
                        $total = $lastDifference;
                        $source = 'realisasi';
                    } else {
                        $total = '0';
                        $source = 'no source';
                    }
                } else {       // Jika ada realisasi sebelumnya, gunakan nilai different
                    if ($lastDifference !== null) {
                        $total = $lastDifference;
                        $source = 'realisasi';
                    } else {
                        // Jika tidak ada realisasi, ambil dari RKAP
                        $total = $this->rkapModel->getTotalByMasterId($acc_id, $department, $unit);
                        $source = 'rkap';
                    }
                }


                return $this->response->setJSON([
                    'status' => 'success',
                    'total' => $total,
                    'source' => $source
                ]);
            } catch (\Exception $e) {
                log_message('error', 'getTotalBudget error: ' . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan saat mengambil data.'
                ]);
            }
        }

        // Jika bukan AJAX, kembalikan error
        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid.'
        ], 400);
    }

    public function generateFormNumber()
    {
        if ($this->request->isAJAX()) {
            $data = $this->request->getJSON();
            $formType = $data->form_type ?? '';

            // Validasi formType
            if (empty($formType)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Form type tidak valid.'
                ]);
            }

            // Generate nomor form menggunakan helper atau logika yang ada
            $formNumber = generate_form_number(session()->get('department'), session()->get('unit_kerja'), $formType);

            return $this->response->setJSON([
                'status' => 'success',
                'form_number' => $formNumber
            ]);
        }

        return $this->response->setJSON(['status' => 'error', 'message' => 'Permintaan tidak valid.']);
    }

    public function storeRealisasi()
    {
        if ($this->request->isAJAX()) {
            try {
                $jsonData = $this->request->getJSON(true);

                // Debug: Log data yang diterima
                log_message('debug', 'Received data: ' . json_encode($jsonData));

                // Konversi nilai desimal dari string ke float dengan mengganti koma menjadi titik
                $totalAkhir = isset($jsonData['totalAkhir']) ? floatval(str_replace(',', '.', $jsonData['totalAkhir'])) : 0;

                // Konversi jumlah tagihan untuk dasar pengenaan pajak
                $dasarPengenaanPajak = isset($jsonData['nominal']) ? floatval(str_replace(',', '.', $jsonData['nominal'])) : 0;

                // Definisikan actual dari data yang diterima
                $actual = isset($jsonData['actual']) ? floatval(str_replace(',', '.', $jsonData['actual'])) : 0;

                // Tentukan jenis potongan pajak yang digunakan
                $potonganPajak = [];
                if (isset($jsonData['isPPN']) && ($jsonData['isPPN'] === '11' || $jsonData['isPPN'] === '12')) {
                    $potonganPajak[] = 'PPN ' . $jsonData['isPPN'];
                }
                if (isset($jsonData['pphType']) && $jsonData['pphType'] !== 'none') {
                    $potonganPajak[] = 'PPH ' . $jsonData['pphType'];
                }

                // Set nilai denganPotongan
                $denganPotongan = !empty($potonganPajak) ? implode(' dan ', $potonganPajak) : 'Tidak ada potongan pajak';

                // Persiapkan data untuk disimpan
                $data = [
                    'formNumber' => $jsonData['formNumber'],
                    'jenis_realisasi' => $jsonData['realisasi'],
                    'acc_no' => $jsonData['acc_no_hidden'],
                    'master_id' => $jsonData['acc_id'],
                    'user' => $jsonData['NamaUser'],
                    'department' => $jsonData['Department'],
                    'unit' => $jsonData['Unit'],
                    'tanggal' => date('Y-m-d', strtotime($jsonData['tanggal'])),
                    'deskripsi' => $jsonData['deskripsi'],
                    'budget' => isset($jsonData['total_budget']) ? floatval(str_replace(',', '.', $jsonData['total_budget'])) : 0,
                    'actual' => empty($potonganPajak) ? $dasarPengenaanPajak : $actual,
                    'different' => isset($jsonData['total_budget']) ?
                        floatval(str_replace(',', '.', $jsonData['total_budget'])) -
                        (empty($potonganPajak) ? $dasarPengenaanPajak : $actual) : 0,
                    'namaVendor' => $jsonData['nama_vendor'] ?? null,
                    'noInvoice' => $jsonData['nomor_invoice'] ?? null,
                    'resiko' => $jsonData['resiko'] ?? null,
                    'resiko_desc' => $jsonData['resiko_desc'] ?? null,
                    'dueDate' => date('Y-m-d', strtotime($jsonData['due_date'])),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'totalAkhir' => $totalAkhir,
                    'dasarPengenaanPajak' => $dasarPengenaanPajak,
                    'denganPotongan' => $denganPotongan, // Menjelaskan jenis potongan pajak yang digunakan
                    'nominalPPN' => isset($jsonData['ppn_amount']) ? floatval(str_replace(',', '.', $jsonData['ppn_amount'])) : 0,
                    'jenisPPH' => isset($jsonData['pphType']) && $jsonData['pphType'] !== 'none' ? $jsonData['pphType'] : null,
                    'nominalPPH' => isset($jsonData['pph_amount']) ? floatval(str_replace(',', '.', $jsonData['pph_amount'])) : 0,
                    'cashOrtransfer' => $jsonData['paymentMethod'] ?? null,
                    'namaPenerima' => $jsonData['namaPenerima'] ?? null,
                    'deptPenerima' => $jsonData['deptPenerima'] ?? null,
                    'namaBank' => $jsonData['nama_bank'] ?? null,
                    'nomorRekening' => $jsonData['nomor_rekening'] ?? null,
                    'swiftCode' => $jsonData['swift_code'] ?? null,
                    'namaPenerimaRekening' => $jsonData['nama_penerima_rekening'] ?? null,
                    'currency' => $jsonData['currency'] ?? null
                ];

                // Simpan data vendor jika ada
                if (
                    !empty($jsonData['nama_vendor']) && $jsonData['paymentMethod'] === 'transfer' &&
                    !empty($jsonData['nama_bank']) && !empty($jsonData['nomor_rekening'])
                ) {

                    $vendorModel = new \App\Models\VendorModel();
                    $vendorData = [
                        'nama_vendor' => $jsonData['nama_vendor'],
                        'metode_pembayaran' => $jsonData['paymentMethod'],
                        'nama_penerima_rekening' => $jsonData['nama_penerima_rekening'] ?? null,
                        'nama_bank' => $jsonData['nama_bank'] ?? null,
                        'nomor_rekening' => $jsonData['nomor_rekening'] ?? null,
                        'swift_code' => $jsonData['swift_code'] ?? null,
                        'nama_penerima' => $jsonData['namaPenerima'] ?? null,
                        'dept_penerima' => $jsonData['deptPenerima'] ?? null
                    ];

                    $vendorModel->saveVendorIfNotExists($vendorData);
                }

                // Simpan data vendor jika ada nama vendor
                if (!empty($jsonData['nama_vendor'])) {
                    $vendorModel = new \App\Models\VendorModel();
                    $vendorData = [
                        'nama_vendor' => $jsonData['nama_vendor'],
                        'metode_pembayaran' => $jsonData['paymentMethod'] ?? 'transfer',
                    ];

                    // Tambahkan data sesuai metode pembayaran
                    if (($jsonData['paymentMethod'] ?? '') === 'transfer') {
                        $vendorData['nama_bank'] = $jsonData['nama_bank'] ?? '';
                        $vendorData['nomor_rekening'] = $jsonData['nomor_rekening'] ?? '';
                        $vendorData['swift_code'] = $jsonData['swift_code'] ?? null;
                        $vendorData['nama_penerima_rekening'] = $jsonData['namaPenerimaRekening'] ?? '';
                    } else if (($jsonData['paymentMethod'] ?? '') === 'cash') {
                        $vendorData['nama_penerima'] = $jsonData['namaPenerima'] ?? null;
                        $vendorData['dept_penerima'] = $jsonData['deptPenerima'] ?? null;
                    }

                    // Simpan atau update vendor
                    $result = $vendorModel->saveVendorIfNotExists($vendorData);
                    log_message('debug', 'Vendor save result: ' . json_encode($result));
                }

                // Debug: Log data yang akan disimpan
                log_message('debug', 'Data to be saved: ' . json_encode($data));

                // Validasi dan simpan data
                if (!$this->realisasiModel->insert($data)) {
                    throw new \Exception('Gagal menyimpan data realisasi: ' . implode(', ', $this->realisasiModel->errors()));
                }

                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Data berhasil disimpan.',
                    'formNumber' => $data['formNumber']
                ]);
            } catch (\Exception $e) {
                log_message('error', 'storeRealisasi error: ' . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan saat menyimpan data.'
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Invalid request'
        ], 400);
    }

    public function recordRealisasi()
    {
        $rkapModel = new RkapModel();
        $session = session();
        $department = $session->get('department'); // Mendapatkan departemen dari sesi
        $unit = $session->get('unit_kerja'); // Mendapatkan unit dari sesi

        // Mengambil opsi acc_no berdasarkan departemen
        $accNos = $rkapModel->where('department', $department)
            ->where('unit', $unit)
            ->findAllAccNos(); // Mengambil acc_no dan description

        // Mengambil opsi tahun
        $yearOptions = $this->realisasiModel->select('YEAR(tanggal) as tahun')
            ->distinct()
            ->orderBy('tahun', 'DESC')
            ->findAll();

        $yearOptionsFormatted = [];
        foreach ($yearOptions as $year) {
            $yearOptionsFormatted[] = ['tahun' => $year['tahun']];
        }

        // Mengambil data realisasi berdasarkan filter
        $earliestBudget = $this->realisasiModel->getEarliestBudgetByYearAndAccNo(date('Y'), array_column($accNos, 'acc_no'));

        $data['title'] = 'Riwayat Realisasi';
        $data['realisasi'] = $this->realisasiModel->findAll();
        $data['earliestBudget'] = $earliestBudget;

        $data['rkapModel'] = $rkapModel; // Kirim model ke view
        $data['accNoOptions'] = $accNos; // Opsi acc_no dengan description
        $data['yearOptions'] = $yearOptionsFormatted; // Opsi tahun

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/record-realisasi', $data)
            . view('budgeting/partials/footer');
    }

    public function getRealisasiData()
    {
        if ($this->request->isAJAX()) {
            try {
                $accNo = $this->request->getPost('acc_no');
                $year = $this->request->getPost('year');
                $acc_id = $this->request->getPost('acc_id');
                $jenis_realisasi = $this->request->getPost('jenis_realisasi');
                $master_id = $this->request->getPost('master_id');
                $session = session();
                $department = $session->get('department');
                $unit = $session->get('unit_kerja');
                $realisasiModel = new RealisasiModel();

                // Ambil data realisasi yang akan dihapus
                // $realisasi = $realisasiModel->find($acc_id);

                // Log parameter yang diterima
                // log_message('info', 'getRealisasiData: acc_no = ' . $accNo);
                // log_message('info', 'getRealisasiData: year = ' . $year);
                // log_message('info', 'getRealisasiData: department = ' . $department);
                // log_message('info', 'getRealisasiData: acc_id = ' . $acc_id);

                // Mengambil data menggunakan model
                $results = $this->realisasiModel->getRealisasiByFiltersnew($accNo, $year, $department, $jenis_realisasi, $acc_id ?? null, $unit ?? null);

                // Log jumlah data yang diambil
                // log_message('info', 'getRealisasiData: Jumlah data yang diambil = ' . count($results));

                // Format data untuk DataTables
                $data = [];
                $no = 1;
                foreach ($results as $row) {
                    // Menentukan jumlah desimal untuk budget
                    $budget = floatval(esc($row['budget']));
                    $budgetDecimals = strlen(explode('.', $row['budget'])[1] ?? '') > 0 ? 2 : 0;
                    $budgetFormatted = number_format($budget, $budgetDecimals, ',', '.');

                    // Menentukan jumlah desimal untuk actual
                    $actual = floatval(esc($row['actual']));
                    $actualDecimals = strlen(explode('.', $row['actual'])[1] ?? '') > 0 ? 2 : 0;
                    $actualFormatted = number_format($actual, $actualDecimals, ',', '.');

                    // Menentukan jumlah desimal untuk difference
                    $difference = floatval(esc($row['different']));
                    $differenceDecimals = strlen(explode('.', $row['different'])[1] ?? '') > 0 ? 2 : 0;
                    $differenceFormatted = $difference < 0 ?
                        '<span class="text-danger">-' . number_format(abs($difference), $differenceDecimals, ',', '.') . '</span>' :
                        number_format($difference, $differenceDecimals, ',', '.');

                    // Tambahkan tombol preview dokumen jika ada
                    $previewButton = '';
                    if (!empty($row['document_finance'])) {
                        $previewButton = '<a href="javascript:void(0);" 
                            class="avatar-text avatar-md text-info preview-existing-doc" 
                            data-document="' . esc($row['document_finance']) . '" 
                            data-bs-toggle="tooltip" 
                            title="' . count(explode(',', $row['document_finance'])) . ' dokumen terupload">
                            <i class="fas fa-file-pdf"></i>
                        </a>';
                    }

                    // Siapkan data untuk PDF
                    $pdfData = [
                        'id' => esc($row['id_realisasi']),
                        'formNumber' => esc($row['formNumber']),
                        'tanggal' => date('d/m/Y', strtotime(esc($row['tanggal']))),
                        'user' => esc($row['user']),
                        'deskripsi' => esc($row['deskripsi']),
                        'description_acc' => esc($row['description_acc']),
                        'acc_no' => esc($row['acc_no']),
                        'actual' => esc($row['actual']),
                        'department' => esc($row['department']),
                        'namaVendor' => esc($row['namaVendor'] ?? ''),
                        'namaBank' => esc($row['namaBank'] ?? ''),
                        'nomorRekening' => esc($row['nomorRekening'] ?? ''),
                        'budget' => esc($row['budget']),
                        'different' => esc($row['different']),
                        'totalakhir' => esc($row['totalAkhir'])
                    ];
                    // Sisanya tetap sama seperti sebelumnya
                    $data[] = [
                        'formNumber' => esc($row['formNumber']),
                        'id_realisasi' => esc($row['id_realisasi']),
                        'no' => $no++,
                        'tanggal' => date('d/m/Y', strtotime(esc($row['tanggal']))),
                        'user' => esc($row['user']),
                        'deskripsi' => esc($row['deskripsi']),
                        'description' => esc($row['description']),
                        'description_acc' => esc($row['description_acc']),
                        'acc_no' => esc($row['acc_no']),
                        'actual' => $actualFormatted,
                        'difference' => $differenceFormatted,
                        'actions' => '<div class="hstack gap-2">
                            <a href="javascript:void(0);" class="avatar-text avatar-md detail-btn" data-id="' . esc($row['id_realisasi']) . '" data-bs-toggle="tooltip" title="Detail">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="javascript:void(0);" class="avatar-text avatar-md edit-btn" data-id="' . esc($row['id_realisasi']) . '" data-bs-toggle="tooltip" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>' .
                            // Cek apakah formNumber mengandung PUM setelah / pertama
                            (strpos(explode('/', esc($row['formNumber']))[1], 'PUM') !== false ?
                                '<a href="javascript:void(0);" 
                               class="avatar-text avatar-md upload-doc-btn ' . (!empty($row['document']) ? 'text-success' : 'text-danger') . '" 
                               data-id="' . esc($row['id_realisasi']) . '" 
                               data-bs-toggle="tooltip" 
                               title="' . (!isset($row['document']) || empty($row['document']) ? 'LPJB belum diupload!' : 'Upload LPJB') . '">
                                <i class="fas fa-upload"></i>
                            </a>' .
                                (!empty($row['document']) ?
                                    '<a href="javascript:void(0);" class="avatar-text avatar-md preview-doc-btn text-primary" data-document="' . esc($row['document']) . '" data-bs-toggle="tooltip" title="Preview LPJB">
                                    <i class="fas fa-file-pdf"></i>
                                </a>' : '') : '') .
                            '<a href="javascript:void(0);" 
                               class="avatar-text avatar-md delete-btn" 
                               data-id="' . esc($row['id_realisasi']) . '" 
                               data-bs-toggle="tooltip" 
                               title="Hapus">
                                <i class="fas fa-trash"></i>
                            </a>' .
                            $previewButton .
                            '<a href="javascript:void(0);" 
                               class="avatar-text avatar-md generate-pdf-btn" 
                               onclick="generatePdf(' . htmlspecialchars(json_encode($pdfData), ENT_QUOTES, 'UTF-8') . ')" 
                               data-bs-toggle="tooltip" 
                               title="Generate Payment Request">
                                <i class="fas fa-file-invoice"></i>
                            </a>
                        </div>',
                        'verifikasi' => (strpos($row['formNumber'], '/PUM/') !== false ?
                            '<div class="hstack gap-2">
                                <a href="javascript:void(0);" 
                                   class="avatar-text avatar-md verify-accmoney-btn ' .
                            (is_null($row['verifGiftmoney']) ? 'disabled text-secondary' : (esc($row['verifAccmoney']) ? 'verified disabled text-success' : 'text-warning')) . '" 
                                   data-id="' . esc($row['id_realisasi']) . '"
                                   data-status="' . (esc($row['verifAccmoney']) ? '1' : '0') . '"
                                   data-verification-date="' . (esc($row['verifAccmoney']) ? date('d/m/Y H:i:s', strtotime($row['verifAccmoney'])) : '') . '"
                                   data-bs-toggle="tooltip" 
                                   ' . (is_null($row['verifGiftmoney']) ? 'style="pointer-events: none; opacity: 0.6;"' : '') . '
                                   title="' . (is_null($row['verifGiftmoney']) ? 'Menunggu verifikasi uang Diserahkan dari Finance' : (esc($row['verifAccmoney']) ? 'Sudah diverifikasi' : 'Verifikasi uang Diterima')) . '">
                                    <i class="fas ' . (is_null($row['verifGiftmoney']) ? 'fa-clock' : (esc($row['verifAccmoney']) ? 'fa-check-circle' : 'fa-hand-holding-usd')) . '"></i>
                                </a>
                                ' . (strpos($row['verifEndmoney'], 'User') === 0 ?
                                '<a href="javascript:void(0);" 
                                   class="avatar-text avatar-md verify-endmoney-btn ' .
                                ($row['verifEndmoney'] && strpos($row['verifEndmoney'], 'User ') === 0 ? 'verified text-success' : 'text-info') . '" 
                                   data-id="' . esc($row['id_realisasi']) . '"
                                   data-status="' . ($row['verifEndmoney'] && strpos($row['verifEndmoney'], 'User ') === 0 ? '1' : '0') . '"
                                   data-verification-date="' . ($row['verifEndmoney'] && strpos($row['verifEndmoney'], 'User ') === 0 ? substr($row['verifEndmoney'], 5) : '') . '"
                                   data-bs-toggle="tooltip" 
                                   data-bs-placement="top"
                                   title="' . ($row['verifEndmoney'] && strpos($row['verifEndmoney'], 'User ') === 0 ? 'Sudah diverifikasi' : 'Verifikasi uang Diterima') . '">
                                    <i class="fas ' . ($row['verifEndmoney'] && strpos($row['verifEndmoney'], 'User ') === 0 ? 'fa-check-circle' : 'fa-hand-holding-dollar') . '"></i>
                                </a>' : '') .
                            '</div>' :
                            ''),
                        'catatan' => esc($row['catatan'])
                    ];
                }

                // Debugging: Log data yang akan dikirim
                log_message('info', 'Data yang dikirim ke DataTables: ' . print_r([
                    'draw' => intval($this->request->getPost('draw')),
                    'data_count' => count($data),
                    'sample_data' => $data[0] ?? 'No data'
                ], true));

                // Kembalikan data dalam format JSON
                return $this->response->setJSON([
                    'draw' => intval($this->request->getPost('draw')),
                    'data' => $data
                ]);
            } catch (\Exception $e) {
                log_message('error', 'Error in getRealisasiData: ' . $e->getMessage());
                return $this->response->setJSON([
                    'draw' => intval($this->request->getPost('draw')),
                    'recordsTotal' => 0,
                    'recordsFiltered' => 0,
                    'data' => [] // Pastikan array kosong jika error
                ]);
            }
        }

        // Jika bukan AJAX, kembalikan error
        return $this->response->setJSON(['error' => 'Invalid request'], 400);
    }

    public function getDetailRealisasi($id = null)
    {
        log_message('info', 'getDetailRealisasi: Memulai dengan ID = ' . $id);

        if ($this->request->isAJAX()) {
            log_message('info', 'getDetailRealisasi: Permintaan AJAX terdeteksi.');

            if ($id === null) {
                log_message('error', 'getDetailRealisasi: ID tidak ditemukan.');
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'ID tidak ditemukan.'
                ], 400);
            }

            log_message('info', 'getDetailRealisasi: Menerima permintaan untuk ID ' . $id);

            // Memperbaiki klausa WHERE dengan menyebutkan tabel
            $data = $this->realisasiModel
                ->select('realisasi.*, tbl_master_data.description_acc, tbl_master_data.acc_no as master_acc_no')
                ->join('tbl_master_data', 'realisasi.acc_no = tbl_master_data.acc_no')
                ->where('realisasi.id', $id) // Menentukan tabel secara eksplisit
                ->first();

            if ($data) {
                log_message('info', 'getDetailRealisasi: Data ditemukan untuk ID ' . $id);
                return $this->response->setJSON([
                    'status' => 'success',
                    'data' => $data
                ]);
            } else {
                log_message('error', 'getDetailRealisasi: Data tidak ditemukan untuk ID ' . $id);
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Data tidak ditemukan.'
                ], 404);
            }
        }

        log_message('error', 'getDetailRealisasi: Permintaan tidak valid.');
        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid.'
        ], 400);
    }

    public function updateRealisasi()
    {
        if ($this->request->isAJAX()) {
            // Mulai transaksi database
            $this->realisasiModel->db->transBegin();

            $request = $this->request->getJSON();

            if (!$request) {
                return $this->response->setJSON([
                    'status'  => 'error',
                    'message' => 'Data JSON tidak valid.'
                ]);
            }

            $id = $request->id ?? null;
            if (!$id) {
                return $this->response->setJSON([
                    'status'  => 'error',
                    'message' => 'ID Realisasi tidak ditemukan.'
                ]);
            }

            // Mengambil data lama untuk perhitungan sisa dana
            $oldData = $this->realisasiModel->find($id);
            if (!$oldData) {
                return $this->response->setJSON([
                    'status'  => 'error',
                    'message' => 'Data Realisasi tidak ditemukan.'
                ]);
            }

            // Mengambil data baru dari request
            $cashOrtransfer = $request->cashOrtransfer ?? null;

            // Format tanggal untuk dueDate
            $dueDate = null;
            if (!empty($request->dueDate)) {
                try {
                    $dueDate = date('Y-m-d', strtotime($request->dueDate));
                } catch (\Exception $e) {
                    log_message('error', 'Error formatting dueDate: ' . $e->getMessage());
                }
            }

            $updateData = [
                'jenis_realisasi' => $request->realisasi ?? null,
                'acc_no'          => $request->acc_no ?? null,
                'tanggal'         => $request->tanggal ?? null,
                'deskripsi'       => $request->deskripsi ?? null,
                'budget'          => $request->budget ?? 0,
                'actual'          => $request->actual ?? 0,
                'different'       => $request->budget - $request->actual,
                // Vendor dan Invoice fields
                'namaVendor'      => $request->namaVendor ?? null,
                'noInvoice'       => $request->noInvoice ?? null,
                'resiko'          => $request->resiko ?? null,
                'resiko_desc'     => $request->resiko_desc ?? null,
                // Payment method fields
                'namaBank'        => $cashOrtransfer === 'transfer' ? $request->namaBank : '',
                'namaPenerimaRekening' => $cashOrtransfer === 'transfer' ? $request->namaPenerimaRekening : '',
                'nomorRekening'   => $cashOrtransfer === 'transfer' ? $request->nomorRekening : '',
                'swiftCode'       => $cashOrtransfer === 'transfer' ? $request->swiftCode : '',
                'dueDate'         => $dueDate,
                'cashOrtransfer'  => $cashOrtransfer,
                'namaPenerima'    => $cashOrtransfer === 'cash' ? $request->namaPenerima : '',
                'deptPenerima'    => $cashOrtransfer === 'cash' ? $request->deptPenerima : '',
                'catatan'         => $request->catatan ?? '',
                'updated_at'      => date('Y-m-d H:i:s'),
                'totalAkhir'      => $request->totalAkhir ?? 0,
            ];
            $updateData['different'] = $updateData['budget'] - $updateData['actual'];


            // Update data realisasi yang di-edit
            if (!$this->realisasiModel->update($id, $updateData)) {
                $this->realisasiModel->db->transRollback();
                return $this->response->setJSON([
                    'status'  => 'error',
                    'message' => 'Gagal memperbarui data Realisasi ke database.'
                ]);
            }
            // Hitung 'different'
            $different = $updateData['budget'] - $updateData['actual'];

            // Mengambil semua transaksi berikutnya yang terkait
            $subsequentTransactions = $this->realisasiModel->getTransactionsAfter(
                $id,
                $updateData['acc_no'],
                $oldData['department'],
                $oldData['unit'],
                $updateData['jenis_realisasi'],
                date('Y', strtotime($updateData['tanggal'])),
                $oldData['master_id']
            );

            // Variabel untuk menyimpan 'different' sebelumnya
            $previousDifferent = $different;

            // Persiapkan data yang akan diupdate untuk semua transaksi sekaligus
            $updateSubData = [];
            foreach ($subsequentTransactions as $transaction) {
                $updatedBudget = $previousDifferent;
                $updatedDifferent = $updatedBudget - $transaction['actual'];

                $updateSubData[] = [
                    'id'          => $transaction['id'],
                    'budget'      => $updatedBudget,
                    'different'   => $updatedDifferent,
                    'updated_at'  => date('Y-m-d H:i:s'),
                ];

                // Update 'previousDifferent' untuk transaksi selanjutnya
                $previousDifferent = $updatedDifferent;
            }

            // Update semua transaksi sekaligus
            foreach ($updateSubData as $subData) {
                if (!$this->realisasiModel->update($subData['id'], [
                    'budget'     => $subData['budget'],
                    'different'  => $subData['different'],
                    'updated_at' => $subData['updated_at'],
                ])) {
                    $this->realisasiModel->db->transRollback();
                    return $this->response->setJSON([
                        'status'  => 'error',
                        'message' => 'Gagal memperbarui transaksi berikutnya.'
                    ]);
                }
            }


            // Commit transaksi database
            if ($this->realisasiModel->db->transStatus() === FALSE) {
                $this->realisasiModel->db->transRollback();
                return $this->response->setJSON([
                    'status'  => 'error',
                    'message' => 'Terjadi kesalahan saat memproses transaksi.'
                ]);
            } else {
                $this->realisasiModel->db->transCommit();
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Data realisasi berhasil diperbarui.'
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid.'
        ], 400);
    }

    public function deleteRealisasi($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request']);
        }

        try {
            $realisasiModel = new RealisasiModel();

            // Ambil data realisasi yang akan dihapus
            $realisasi = $realisasiModel->find($id);

            if (!$realisasi) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Data realisasi tidak ditemukan'
                ]);
            }

            // Ambil tahun dari tanggal realisasi
            $year = date('Y', strtotime($realisasi['tanggal']));

            // Ambil transaksi berikutnya
            $subsequentTransactions = $realisasiModel->getTransactionsAfter(
                $id,
                $realisasi['acc_no'],
                $realisasi['department'],
                $realisasi['unit'],
                $realisasi['jenis_realisasi'],
                $year,
                $realisasi['master_id']
            );

            // Hapus realisasi
            if ($realisasiModel->delete($id)) {
                // Update transaksi berikutnya jika ada
                if (!empty($subsequentTransactions)) {
                    foreach ($subsequentTransactions as $transaction) {
                        // Hitung ulang budget dan different
                        $previousTransaction = $realisasiModel->where('id <', $transaction['id'])
                            ->where('master_id', $transaction['master_id'])
                            ->where('acc_no', $transaction['acc_no'])
                            ->where('department', $transaction['department'])
                            ->where('unit', $transaction['unit'])
                            ->where('jenis_realisasi', $transaction['jenis_realisasi'])
                            ->where('YEAR(tanggal)', $year)
                            ->orderBy('id', 'DESC')
                            ->first();

                        $newBudget = $previousTransaction ? $previousTransaction['different'] : $transaction['budget'];
                        $newDifferent = $newBudget - $transaction['actual'];

                        $realisasiModel->update($transaction['id'], [
                            'budget' => $newBudget,
                            'different' => $newDifferent
                        ]);
                    }
                }

                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Data realisasi berhasil dihapus',
                    'csrfHash' => csrf_hash() // Tambahkan CSRF hash baru
                ]);
            }

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Gagal menghapus data realisasi'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error deleting realisasi: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat menghapus data'
            ]);
        }
    }

    public function getTotalRKAP()
    {
        if ($this->request->isAJAX()) {
            $acc_no = $this->request->getPost('acc_no');
            $acc_id = $this->request->getPost('acc_id'); // Mengambil acc_id
            $year = $this->request->getPost('year');
            $jenis_realisasi = $this->request->getPost('jenis_realisasi');

            // Mendapatkan unit dan department dari session
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            // log_message('debug', 'Realisasi data: ' . json_encode($realisasi));

            // Validasi input
            if (empty($acc_no) || empty($acc_id) || empty($year) || empty($jenis_realisasi)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Input tidak lengkap.'
                ]);
            }

            // Logika untuk mendapatkan total berdasarkan jenis_realisasi dan acc_id
            if ($jenis_realisasi === 'RKAP BUDGETTER') {
                // Misalnya, menggunakan RkapModel untuk jenis RKAP BUDGETTER
                // Pastikan metode getTotalByAccId terdefinisi di RkapModel
                $total = $this->rkapModel->getTotalByAccId($acc_id, $year, $unit, $department);
            } else {
                // Misalnya, untuk jenis lainnya, total bisa diatur ke 0 atau sesuai kebutuhan
                $total = 0;
            }

            return $this->response->setJSON([
                'status' => 'success',
                'total' => $total
            ]);
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid.'
        ], 400);
    }

    public function exportRkapBudgetter($year)
    {
        // Ambil data yang diperlukan dari model
        $model = new \App\Models\BudgetModel();
        $data = $model->getRkapBudgetter($year);

        // Buat Spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Header Tabel
        $sheet->setCellValue('A1', 'No');
        $sheet->setCellValue('B1', 'Acc No');
        $sheet->setCellValue('C1', 'Deskripsi');
        $sheet->setCellValue('D1', 'Budget');
        $sheet->setCellValue('E1', 'Realisasi RKAP BUDGETTER');
        $sheet->setCellValue('F1', 'Sisa Anggaran');
        $sheet->setCellValue('G1', 'Presentase (%)');

        // Isi Data
        $row = 2;
        $no = 1;
        foreach ($data as $item) {
            $sheet->setCellValue('A' . $row, $no++);
            $sheet->setCellValue('B' . $row, $item['acc_no']);
            $sheet->setCellValue('C' . $row, $item['description']);
            $sheet->setCellValue('D' . $row, $item['budget']);
            $sheet->setCellValue('E' . $row, $item['realisasi_budgetter']);
            $sheet->setCellValue('F' . $row, $item['sisa_anggaran']);
            $sheet->setCellValue('G' . $row, $item['persentase']);
            $row++;
        }

        // Format sebagai angka untuk kolom Budget dan Realisasi
        foreach (range('D', 'G') as $col) {
            $sheet->getStyle($col)->getNumberFormat()->setFormatCode('#,##0.00');
        }

        // Buat Writer dan Simpan ke Output
        $writer = new Xlsx($spreadsheet);
        $filename = 'Rkap_Budgetter_' . $year . '.xlsx';

        // Headers untuk download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportRkapNonBudgetter($year)
    {
        // Ambil data yang diperlukan dari model
        $model = new \App\Models\BudgetModel();
        $data = $model->getRkapNonBudgetter($year);

        // Buat Spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Header Tabel
        $sheet->setCellValue('A1', 'No');
        $sheet->setCellValue('B1', 'Acc No');
        $sheet->setCellValue('C1', 'Deskripsi');
        $sheet->setCellValue('D1', 'Realisasi RKAP NON BUDGETTER');

        // Isi Data
        $row = 2;
        $no = 1;
        foreach ($data as $item) {
            $sheet->setCellValue('A' . $row, $no++);
            $sheet->setCellValue('B' . $row, $item['acc_no']);
            $sheet->setCellValue('C' . $row, $item['description']);
            $sheet->setCellValue('D' . $row, $item['realisasi_non_budgetter']);
            $row++;
        }

        // Format sebagai angka untuk kolom Realisasi
        $sheet->getStyle('D')->getNumberFormat()->setFormatCode('#,##0.00');

        // Buat Writer dan Simpan ke Output
        $writer = new Xlsx($spreadsheet);
        $filename = 'Rkap_Non_Budgetter_' . $year . '.xlsx';

        // Headers untuk download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function autocompleteBank()
    {
        if ($this->request->isAJAX()) {
            $query = $this->request->getGet('term');
            $banks = $this->bankModel->searchBanks($query);

            $result = [];
            foreach ($banks as $bank) {
                $result[] = [
                    'id' => $bank['id'],
                    'label' => $bank['bank'],
                    'value' => $bank['bank']
                ];
            }

            return $this->response->setJSON($result);
        }

        return $this->response->setJSON(['error' => 'Permintaan tidak valid.'], 400);
    }

    // Metode untuk mendapatkan acc_primary berdasarkan year
    public function getAccPrimary()
    {
        if ($this->request->isAJAX()) {
            $year = $this->request->getPost('year');
            if (!$year) {
                return $this->response->setJSON(['error' => 'Year tidak valid.'], 400);
            }

            // Ambil acc_primary berdasarkan year. Asumsikan ada relasi antara RKAP dan Acc Primary.
            // Jika tidak ada, sesuaikan logika pengambilan acc_primary.
            $accPrimary = $this->budgetingModel->getAccPrimaryByYear($year);
            return $this->response->setJSON($accPrimary);
        }

        return $this->response->setJSON(['error' => 'Permintaan tidak valid.'], 400);
    }

    // Metode untuk mendapatkan acc_secondary berdasarkan acc_primary
    public function getAccSecondary()
    {
        if ($this->request->isAJAX()) {
            $accPrimary = $this->request->getPost('acc_primary');
            if (!$accPrimary) {
                return $this->response->setJSON(['error' => 'Acc Primary tidak valid.'], 400);
            }

            $accSecondary = $this->budgetingModel->getAccSecondaryByPrimary($accPrimary);
            return $this->response->setJSON($accSecondary);
        }

        return $this->response->setJSON(['error' => 'Permintaan tidak valid.'], 400);
    }

    // Metode untuk mendapatkan acc_no berdasarkan acc_secondary
    public function getAccNo()
    {
        if ($this->request->isAJAX()) {
            $accPrimary = $this->request->getPost('acc_primary');
            $accSecondary = $this->request->getPost('acc_secondary');

            $accNo = $this->budgetingModel
                ->where('acc_primary', $accPrimary)
                ->where('acc_secondary', $accSecondary)
                ->distinct()
                ->select('id, acc_no, description_acc')
                ->orderBy('acc_no', 'ASC')
                ->findAll();

            return $this->response->setJSON($accNo);
        }
        return $this->response->setJSON(['error' => 'Invalid request']);
    }

    public function getRkapData()
    {
        if ($this->request->isAJAX()) {
            $year = $this->request->getPost('year');
            $accPrimary = $this->request->getPost('acc_primary');
            $accSecondary = $this->request->getPost('acc_secondary');
            $accNo = $this->request->getPost('acc_no');
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            $builder = $this->rkapModel->select('
                rkaps.id,
                rkaps.acc_no,
                rkaps.description,
                rkaps.description_sec,
                rkaps.schedule,
                rkaps.jan,
                rkaps.feb,
                rkaps.mar,
                rkaps.apr,
                rkaps.mei,
                rkaps.jun,
                rkaps.jul,
                rkaps.agu,
                rkaps.sep,
                rkaps.okt,
                rkaps.nov,
                rkaps.des,
                rkaps.total,
                tbl_master_data.description_acc
            ')
                ->join('tbl_master_data', 'tbl_master_data.acc_no = rkaps.acc_no')
                ->where('year', $year)
                ->where('unit', $unit)
                ->where('department', $department);

            if ($accPrimary) {
                $builder->where('rkaps.acc_primary', $accPrimary);
            }
            if ($accSecondary) {
                $builder->where('rkaps.acc_secondary', $accSecondary);
            }
            if ($accNo) {
                $builder->where('rkaps.acc_no', $accNo);
            }

            $data = $builder->orderBy('acc_no', 'ASC')->findAll();

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $data
            ]);
        }
        return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request']);
    }

    public function storeRkapItems()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Permintaan tidak valid']);
        }

        try {
            // Mengambil data dari request
            $year = $this->request->getPost('year');
            $accPrimary = $this->request->getPost('acc_primary');
            $accSecondary = $this->request->getPost('acc_secondary');
            $descriptionSecondary = $this->request->getPost('description_secondary');
            $accNo = $this->request->getPost('acc_no');
            $department = $this->request->getPost('department');
            $unitKerja = $this->request->getPost('unit_kerja');
            $master_id = $this->request->getPost('id'); // Mengambil 'id' dari request dan menyimpannya sebagai 'master_id'
            $items = $this->request->getPost('items');

            // Validasi mandatory fields
            if (!$year || !$accPrimary || !$accSecondary || !$accNo || !$unitKerja || !$department || !$master_id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Data wajib tidak lengkap.'
                ]);
            }

            // Debug log
            log_message('info', 'Received data: ' . json_encode([
                'master_id' => $master_id,
                'acc_primary' => $accPrimary,
                'acc_secondary' => $accSecondary,
                'description_secondary' => $descriptionSecondary,
                'acc_no' => $accNo,
                'unit_kerja' => $unitKerja
            ]));

            // Persiapkan data untuk batch insert
            $batchData = [];
            foreach ($items as $item) {
                if (!empty($item['description'])) {
                    $batchData[] = [
                        'year'                 => $year,
                        'acc_primary'          => $accPrimary,
                        'acc_secondary'        => $accSecondary,
                        'description_sec'      => $descriptionSecondary,
                        'acc_no'               => $accNo,
                        'department'           => $department,
                        'unit'                 => $unitKerja,
                        'description'          => $item['description'],
                        'jan'                  => $item['jan'] ?? 0,
                        'feb'                  => $item['feb'] ?? 0,
                        'mar'                  => $item['mar'] ?? 0,
                        'apr'                  => $item['apr'] ?? 0,
                        'mei'                  => $item['mei'] ?? 0,
                        'jun'                  => $item['jun'] ?? 0,
                        'jul'                  => $item['jul'] ?? 0,
                        'agu'                  => $item['agu'] ?? 0,
                        'sep'                  => $item['sep'] ?? 0,
                        'okt'                  => $item['okt'] ?? 0,
                        'nov'                  => $item['nov'] ?? 0,
                        'des'                  => $item['des'] ?? 0,
                        'total'                => $item['total'] ?? 0,
                        'master_id'            => $master_id, // Menyimpan 'id' sebagai 'master_id'
                        'created_at'           => date('Y-m-d H:i:s'),
                        'created_by'           => session()->get('user_id')
                    ];
                }
            }

            if (!empty($batchData)) {
                if (!$this->rkapModel->insertBatch($batchData)) {
                    throw new \Exception('Gagal menyimpan data.');
                }
            }

            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Data berhasil disimpan.'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[storeRkapItems] Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat menyimpan data.'
            ]);
        }
    }

    public function getAvailableYears()
    {
        if ($this->request->isAJAX()) {
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            $years = $this->rkapModel
                ->where('unit', $unit)
                ->where('department', $department)
                ->distinct()
                ->select('year')
                ->orderBy('year', 'DESC')
                ->findAll();

            $yearList = array_column($years, 'year');
            return $this->response->setJSON($yearList);
        }
        return $this->response->setJSON(['error' => 'Invalid request']);
    }

    public function getAvailableAccSecondary()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['error' => 'Invalid request']);
        }

        try {
            $accPrimary = $this->request->getPost('acc_primary');
            $year = $this->request->getPost('year');

            $db = \Config\Database::connect('invoices');
            $result = $db->table('tbl_master_data')
                ->select('acc_secondary, description_secondary')
                ->where('acc_primary', $accPrimary)
                ->groupBy('acc_secondary, description_secondary')
                ->orderBy('acc_secondary', 'ASC')
                ->get()
                ->getResultArray();

            return $this->response->setJSON($result);
        } catch (\Exception $e) {
            log_message('error', '[getAvailableAccSecondary] Error: ' . $e->getMessage());
            return $this->response->setJSON(['error' => $e->getMessage()]);
        }
    }

    public function getAvailableAccNo()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['error' => 'Invalid request']);
        }

        try {
            $accPrimary = $this->request->getPost('acc_primary');
            $accSecondary = $this->request->getPost('acc_secondary');
            $year = $this->request->getPost('year');
            $unit = $this->request->getPost('unit');
            $department = $this->request->getPost('department');

            // Log input parameters
            log_message('debug', 'getAvailableAccNo parameters: ' . json_encode([
                'acc_primary' => $accPrimary,
                'acc_secondary' => $accSecondary,
                'year' => $year,
                'unit' => $unit,
                'department' => $department
            ]));

            // Validasi input
            if (!$accPrimary || !$accSecondary || !$year || !$unit || !$department) {
                return $this->response->setJSON([
                    'error' => 'Missing required parameters',
                    'debug' => [
                        'acc_primary' => $accPrimary,
                        'acc_secondary' => $accSecondary,
                        'year' => $year,
                        'unit' => $unit,
                        'department' => $department
                    ]
                ]);
            }

            $result = $this->rkapModel->distinct()
                ->select('rkaps.acc_no, rkaps.description_sec, tbl_master_data.description_acc')
                ->join('tbl_master_data', 'tbl_master_data.id = rkaps.master_id')
                ->where([
                    'rkaps.acc_primary' => $accPrimary,
                    'rkaps.acc_secondary' => $accSecondary,
                    'rkaps.year' => $year,
                    'rkaps.unit' => $unit,
                    'rkaps.department' => $department
                ])
                ->orderBy('rkaps.acc_no', 'ASC')
                ->findAll();

            // Log result
            log_message('debug', 'getAvailableAccNo result count: ' . count($result));

            return $this->response->setJSON($result);
        } catch (\Exception $e) {
            log_message('error', '[getAvailableAccNo] Error: ' . $e->getMessage());
            log_message('error', '[getAvailableAccNo] Stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'error' => 'Server error occurred',
                'message' => $e->getMessage(),
                'debug' => true
            ]);
        }
    }

    public function deleteRkapItem($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Permintaan tidak valid'
            ]);
        }

        try {
            // Cek apakah data berhasil dihapus
            if ($this->rkapModel->delete($id)) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Data berhasil dihapus',
                    'csrfHash' => csrf_hash()
                ]);
            }

            // Jika sampai di sini berarti proses delete gagal
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Gagal menghapus data',
                'csrfHash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error deleting RKAP item: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat menghapus data',
                'csrfHash' => csrf_hash()
            ]);
        }
    }

    public function getRkapItem($id)
    {
        $realisasiModel = new RealisasiModel();

        $rkap = $this->rkapModel->find($id);
        if (!$rkap) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Data tidak ditemukan']);
        }

        // Tambahkan pengecekan realisasi
        $rkap['has_realisasi'] = $realisasiModel->where('master_id', $rkap['id'])->countAllResults() > 0;


        return $this->response->setJSON([
            'status' => 'success',
            'data' => $rkap
        ]);
    }

    public function updateRkapItem()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method',
                'csrfHash' => csrf_hash() // Selalu sertakan CSRF hash baru
            ]);
        }

        $data = $this->request->getPost();

        try {
            // Lakukan validasi dan update data
            $rkapModel = new RkapModel();
            $updated = $rkapModel->update($data['id'], [
                'description' => $data['description'],
                'jan' => $data['jan'],
                'feb' => $data['feb'],
                'mar' => $data['mar'],
                'apr' => $data['apr'],
                'mei' => $data['mei'],
                'jun' => $data['jun'],
                'jul' => $data['jul'],
                'agu' => $data['agu'],
                'sep' => $data['sep'],
                'okt' => $data['okt'],
                'nov' => $data['nov'],
                'des' => $data['des'],
                'total' => $data['total']
            ]);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Data berhasil diperbarui',
                'csrfHash' => csrf_hash() // Selalu sertakan CSRF hash baru
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Gagal memperbarui data: ' . $e->getMessage(),
                'csrfHash' => csrf_hash() // Selalu sertakan CSRF hash baru
            ]);
        }
    }

    public function refreshCsrf()
    {
        return $this->response->setJSON([
            'token' => csrf_hash()
        ]);
    }

    public function uploadDocument()
    {
        if ($this->request->isAJAX()) {
            try {
                $realisasiId = $this->request->getPost('realisasi_id');
                $file = $this->request->getFile('document');

                // Debug info
                log_message('debug', 'Upload attempt - RealisasiID: ' . $realisasiId);

                // Cek file lama
                $realisasi = $this->realisasiModel->find($realisasiId);
                if ($realisasi && !empty($realisasi['document'])) {
                    $oldFile = FCPATH . 'uploads/pum_doc/' . $realisasi['document'];
                    if (file_exists($oldFile)) {
                        unlink($oldFile); // Hapus file lama
                        log_message('debug', 'Old file deleted: ' . $oldFile);
                    }
                }

                if (!$file->isValid()) {
                    log_message('error', 'Invalid file: ' . $file->getErrorString());
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Dokumen tidak valid: ' . $file->getErrorString(),
                        'csrfHash' => csrf_hash()
                    ]);
                }

                // Generate random name
                $newName = $file->getRandomName();

                // Debug path info
                $uploadPath = FCPATH . 'uploads/pum_doc';
                log_message('debug', 'Upload path: ' . $uploadPath);

                // Check if directory exists and is writable
                if (!is_dir($uploadPath)) {
                    log_message('error', 'Upload directory does not exist: ' . $uploadPath);
                    mkdir($uploadPath, 0775, true);
                }

                if (!is_writable($uploadPath)) {
                    log_message('error', 'Upload directory is not writable: ' . $uploadPath);
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Directory tidak dapat ditulis',
                        'csrfHash' => csrf_hash()
                    ]);
                }

                // Pindahkan file ke folder upload
                try {
                    $file->move($uploadPath, $newName);

                    // Debug successful move
                    log_message('debug', 'File successfully moved to: ' . $uploadPath . '/' . $newName);

                    // Update database dengan nama file baru
                    $this->realisasiModel->update($realisasiId, [
                        'document' => $newName,
                        'tanggal_upload_lpjb' => date('Y-m-d H:i:s')
                    ]);

                    // Ambil data terbaru
                    $updatedData = $this->realisasiModel->find($realisasiId);

                    // Kirim notifikasi ke finance
                    $this->sendDocumentUploadEmail(
                        $updatedData,
                        $newName,
                        $this->request->getPost('catatan')
                    );

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Dokumen berhasil diupload',
                        'csrfHash' => csrf_hash()
                    ]);
                } catch (\Exception $e) {
                    log_message('error', 'File move error: ' . $e->getMessage());
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Gagal memindahkan file: ' . $e->getMessage(),
                        'csrfHash' => csrf_hash()
                    ]);
                }
            } catch (\Exception $e) {
                log_message('error', 'Upload error: ' . $e->getMessage());
                log_message('error', 'Stack trace: ' . $e->getTraceAsString());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                    'csrfHash' => csrf_hash()
                ]);
            }
        }
        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Invalid request',
            'csrfHash' => csrf_hash()
        ]);
    }

    private function sendDocumentUploadEmail($realisasiData, $uploadedDocuments, $catatan = '')
    {
        try {
            // Cek apakah ini PUM
            if (strpos($realisasiData['formNumber'], '/PUM/') === false) {
                return false;
            }

            // Ambil email finance department
            $departmentModel = new DepartmentModel();
            $unitModel = new UnitModel();
            $unitDept = $unitModel->where('unit', $realisasiData['unit'])->first();

            $financeDept = $departmentModel->where('department', 'Finance')
                ->where('unit', $unitDept['id'] ?? null)
                ->first();

            if (!$financeDept || empty($financeDept['email'])) {
                log_message('error', "Email finance department tidak ditemukan");
                return false;
            }

            $config = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.humi.co.id',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'Hits@2524114#',
                'SMTPPort' => 587,
                'SMTPCrypto' => 'tls',
                'mailType' => 'html',
                'charset' => 'utf-8',
                'wordWrap' => true
            ];

            $email = \Config\Services::email($config);
            $email->setFrom('<EMAIL>', 'HALO - HUMI');
            $email->setTo($financeDept['email']);
            $email->setBCC('<EMAIL>');
            $email->setSubject("Dokumen PUM Telah Diupload - {$realisasiData['formNumber']}");

            // Sanitize semua data
            $formNumber = htmlspecialchars($realisasiData['formNumber'], ENT_QUOTES, 'UTF-8');
            $department = htmlspecialchars($realisasiData['department'], ENT_QUOTES, 'UTF-8');
            $unit = htmlspecialchars($realisasiData['unit'], ENT_QUOTES, 'UTF-8');
            $user = htmlspecialchars($realisasiData['user'], ENT_QUOTES, 'UTF-8');
            $deskripsi = htmlspecialchars($realisasiData['deskripsi'], ENT_QUOTES, 'UTF-8');
            $catatan = htmlspecialchars($catatan, ENT_QUOTES, 'UTF-8');

            $emailContent = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
                    <h2 style="color: #2c3e50; margin: 0;">
                        <i class="fas fa-file-upload" style="color: #007bff; margin-right: 10px;"></i>
                        Dokumen PUM Telah Diupload
                    </h2>
                </div>

                <div style="padding: 25px; background-color: #ffffff;">
                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px;">
                            Detail Upload
                        </h3>
                        
                        <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                            <tr>
                                <td style="width: 35%; padding: 12px 10px; background-color: #f8f9fa;">Nomor PUM</td>
                                <td style="width: 65%; padding: 12px 10px;">' . $formNumber . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Tanggal Upload</td>
                                <td style="padding: 12px 10px;">' . date('d/m/Y H:i') . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Department</td>
                                <td style="padding: 12px 10px;">' . $department . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Unit</td>
                                <td style="padding: 12px 10px;">' . $unit . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">User</td>
                                <td style="padding: 12px 10px;">' . $user . '</td>
                            </tr>
                        </table>
                    </div>'

                . (!empty($catatan) ? '
                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 15px;">
                        <h4 style="color: #856404; margin: 0 0 10px 0;">Catatan Upload:</h4>
                        <p style="margin: 0; color: #856404;">' . $catatan . '</p>
                    </div>' : '') . '

                    <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; text-align: center;">
                        <p style="margin: 0; color: #6c757d; font-size: 0.9em;">
                            <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                            Email notifikasi otomatis ini dikirim ke Departemen Finance untuk proses verifikasi dokumen.
                        </p>
                    </div>
                </div>
            </div>';

            // Tambahkan header charset
            $email->setHeader('Content-Type', 'text/html; charset=UTF-8');
            $email->setMessage($emailContent);

            if (!$email->send()) {
                log_message('error', "Gagal mengirim email notifikasi dokumen ke finance");
                return false;
            }

            log_message('info', "Email notifikasi dokumen terkirim ke finance: {$financeDept['email']}");
            return true;
        } catch (\Exception $e) {
            log_message('error', "Error sending document upload email: " . $e->getMessage());
            return false;
        }
    }

    public function formLpjb()
    {
        $data['title'] = 'Form LPJB';

        // Ambil department dan unit dari session
        $department = session()->get('department');
        $unit = session()->get('unit_kerja');

        // Ambil form numbers yang sesuai
        $realisasiModel = new \App\Models\RealisasiModel();
        $data['formNumbers'] = $realisasiModel->getPumFormNumbers($department, $unit);

        // Debug
        // var_dump($data['formNumbers']); die;

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/form-lpjb', $data)
            . view('budgeting/partials/footer');
    }

    public function storeLpjb()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permintaan tidak valid']);
        }

        $lpjbModel = new LpjbModel();

        try {
            // Debug: log data yang diterima
            log_message('debug', 'Data diterima: ' . json_encode($this->request->getPost()));

            // Ambil data detail dari request
            $details = json_decode($this->request->getPost('details'), true);

            if (empty($details)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Detail LPJB tidak boleh kosong'
                ]);
            }

            // Persiapkan data untuk batch insert
            $batchData = [];
            foreach ($details as $detail) {
                $batchData[] = [
                    'formNumber' => $this->request->getPost('formNumber'),
                    'description' => $this->request->getPost('description'),
                    'tanggal' => date('Y-m-d', strtotime($this->request->getPost('tanggal'))),
                    'no_acc' => $this->request->getPost('no_acc'),
                    'department' => $this->request->getPost('department'),
                    'unit' => $this->request->getPost('unit'),
                    'description_sec' => $this->request->getPost('description_acc'),
                    'tanggal_detail' => date('Y-m-d', strtotime($detail['tanggal'])),
                    'keterangan' => $detail['keterangan'],
                    'anggaran' => str_replace('.', '', $this->request->getPost('total_anggaran')),
                    'realisasi' => str_replace('.', '', $detail['realisasi']),
                    'sisa_anggaran' => str_replace('.', '', $detail['sisa_anggaran']),
                    'total_realisasi' => str_replace('.', '', $this->request->getPost('total_realisasi')),
                    'total_sisa_anggaran' => str_replace('.', '', $this->request->getPost('total_sisa_anggaran')),
                    'pattycash_sisa' => str_replace('.', '', $this->request->getPost('pattycash_sisa')), // Tambahkan ini
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
            }

            // Debug: log data yang akan disimpan
            log_message('debug', 'Data yang akan disimpan: ' . json_encode($batchData));

            // Insert batch data
            if (!empty($batchData)) {
                if (!$lpjbModel->insertBatch($batchData)) {
                    log_message('error', 'Gagal memasukkan data batch: ' . json_encode($lpjbModel->errors()));
                    throw new \Exception('Gagal menyimpan data ke database');
                }

                // Penambahan kode untuk memperbarui 'actual' dan menghitung ulang 'different'
                $formNumber = $this->request->getPost('formNumber');
                $totalRealisasi = str_replace('.', '', $this->request->getPost('total_realisasi'));
                $realisasiModel = new RealisasiModel();

                // Cari record realisasi berdasarkan formNumber
                $realisasi = $realisasiModel->where('formNumber', $formNumber)->first();

                if ($realisasi) {
                    // Ambil data yang diperlukan
                    $jenis_realisasi = $realisasi['jenis_realisasi'];
                    $acc_no = $realisasi['acc_no'];
                    $department = $realisasi['department'];
                    $unit = $realisasi['unit'];
                    $tanggal = $realisasi['tanggal'];
                    $year = date('Y', strtotime($tanggal));
                    $actual = $realisasi['actual'];

                    // Optimalisasi pengecekan verifEndmoney
                    $verifEndmoney = ($actual >= $totalRealisasi) ? 'Finance' : 'User';
                    $realisasiModel->update($realisasi['id'], ['verifEndmoney' => $verifEndmoney]);

                    // Update kolom 'actual' dan 'different'
                    $realisasiModel->update($realisasi['id'], [
                        'actual' => $totalRealisasi,
                        'different' => $realisasi['budget'] - $totalRealisasi
                    ]);

                    log_message('debug', 'Realisasi diperbarui untuk formNumber: ' . $formNumber);

                    // Ambil transaksi berikutnya
                    $subsequentTransactions = $realisasiModel->getTransactionsAfter(
                        $realisasi['id'],
                        $acc_no,
                        $department,
                        $unit,
                        $jenis_realisasi,
                        $year,
                        $realisasi['master_id']
                    );

                    // Variabel untuk menyimpan 'different' sebelumnya
                    $previousDifferent = $realisasi['budget'] - $totalRealisasi;

                    // Persiapkan data yang akan diupdate untuk semua transaksi sekaligus
                    foreach ($subsequentTransactions as $transaction) {
                        $updatedBudget = $previousDifferent;
                        $updatedDifferent = $updatedBudget - $transaction['actual'];

                        // Update transaksi
                        if (!$realisasiModel->update($transaction['id'], [
                            'budget' => $updatedBudget,
                            'different' => $updatedDifferent,
                            'updated_at' => date('Y-m-d H:i:s'),
                        ])) {
                            log_message('error', 'Gagal memperbarui transaksi ID ' . $transaction['id']);
                            // Tidak menggunakan transaksi, jadi langsung lanjut
                            continue;
                        }

                        // Log pembaruan untuk debugging
                        log_message('info', "Transaksi ID {$transaction['id']} diperbarui: budget={$updatedBudget}, different={$updatedDifferent}");

                        // Set 'previousDifferent' untuk iterasi berikutnya
                        $previousDifferent = $updatedDifferent;
                    }

                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'Data LPJB berhasil disimpan dan realisasi diperbarui',
                        'rowCount' => count($batchData)
                    ]);
                } else {
                    log_message('error', 'Realisasi tidak ditemukan untuk formNumber: ' . $formNumber);
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Realisasi tidak ditemukan untuk formNumber yang diberikan'
                    ]);
                }
            }

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Tidak ada data yang disimpan'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error di storeLpjb: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal menyimpan data: ' . $e->getMessage()
            ]);
        }
    }

    public function recordLpjb()
    {
        $data['title'] = 'Record LPJB';
        $lpjbModel = new LpjbModel();
        $data['lpjb'] = $lpjbModel->where('department', session('department'))->findAll();

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/record-lpjb', $data)
            . view('budgeting/partials/footer');
    }

    public function updateLpjb()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Permintaan tidak valid']);
        }

        try {
            $lpjbModel = new LpjbModel();

            // Ambil data dari request
            $formNumber = $this->request->getPost('formNumber');
            $details = $this->request->getPost('details');

            // Pastikan $details adalah array
            if (!is_array($details)) {
                // Jika $details adalah string (JSON), decode menjadi array
                $details = json_decode($details, true);
            }

            if (empty($details) || empty($formNumber)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Data tidak lengkap'
                ]);
            }

            // Ambil data existing
            $existingData = $lpjbModel->where('formNumber', $formNumber)->findAll();
            if (empty($existingData)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Data tidak ditemukan'
                ]);
            }

            try {
                // Hapus data lama terlebih dahulu
                if (!$lpjbModel->where('formNumber', $formNumber)->delete()) {
                    throw new \Exception('Gagal menghapus data lama');
                }

                // Siapkan data baru
                $totalRealisasi = 0;
                $insertData = [];
                $anggaran = (int)$existingData[0]['anggaran'];
                $runningTotal = 0;

                // Hitung total realisasi terlebih dahulu
                foreach ($details as $detail) {
                    $realisasi = (int)str_replace('.', '', $detail['realisasi']);
                    $totalRealisasi += $realisasi;
                }

                // Total sisa anggaran akhir
                $totalSisaAnggaran = $anggaran - $totalRealisasi;
                $runningRealisasi = 0;

                // Buat data untuk setiap detail
                foreach ($details as $detail) {
                    $realisasi = (int)str_replace('.', '', $detail['realisasi']);
                    $runningRealisasi += $realisasi;

                    $insertData[] = [
                        'formNumber' => $formNumber,
                        'description' => $existingData[0]['description'],
                        'tanggal' => $existingData[0]['tanggal'],
                        'no_acc' => $existingData[0]['no_acc'],
                        'department' => $existingData[0]['department'],
                        'unit' => $existingData[0]['unit'],
                        'description_sec' => $existingData[0]['description_sec'],
                        'tanggal_detail' => date('Y-m-d', strtotime($detail['tanggal_detail'])),
                        'keterangan' => $detail['keterangan'],
                        'anggaran' => $anggaran,
                        'realisasi' => $realisasi,
                        'sisa_anggaran' => $anggaran - $runningRealisasi, // Sisa anggaran per baris berbeda
                        'total_realisasi' => $totalRealisasi,             // Total realisasi sama untuk semua
                        'total_sisa_anggaran' => $totalSisaAnggaran      // Total sisa anggaran sama untuk semua
                    ];
                }

                // Insert data baru
                if (!empty($insertData)) {
                    $insertResult = $lpjbModel->insertBatch($insertData);

                    if ($insertResult === false) {
                        // Jika gagal, kembalikan data lama
                        foreach ($existingData as $oldData) {
                            $lpjbModel->insert($oldData);
                        }
                        throw new \Exception('Gagal menyimpan data baru');
                    }
                }

                // Penambahan kode untuk memperbarui 'actual' dan menghitung ulang 'different'
                $totalRealisasiNumeric = str_replace('.', '', $this->request->getPost('total_realisasi'));
                $realisasiModel = new RealisasiModel();

                // Cari record realisasi berdasarkan formNumber
                $realisasi = $realisasiModel->where('formNumber', $formNumber)->first();

                if ($realisasi) {
                    // Ambil data yang diperlukan
                    $jenis_realisasi = $realisasi['jenis_realisasi'];
                    $acc_no = $realisasi['acc_no'];
                    $department = $realisasi['department'];
                    $unit = $realisasi['unit'];
                    $tanggal = $realisasi['tanggal'];
                    $year = date('Y', strtotime($tanggal));

                    // Update kolom 'actual' dan 'different'
                    $realisasiModel->update($realisasi['id'], [
                        'actual' => $totalRealisasiNumeric,
                        'different' => $realisasi['budget'] - $totalRealisasiNumeric
                    ]);

                    log_message('debug', 'Realisasi diperbarui untuk formNumber: ' . $formNumber);

                    // Ambil transaksi berikutnya
                    $subsequentTransactions = $realisasiModel->getTransactionsAfter(
                        $realisasi['id'],
                        $acc_no,
                        $department,
                        $unit,
                        $jenis_realisasi,
                        $year,
                        $realisasi['master_id']
                    );

                    // Variabel untuk menyimpan 'different' sebelumnya
                    $previousDifferent = $realisasi['budget'] - $totalRealisasiNumeric;

                    // Persiapkan data yang akan diupdate untuk semua transaksi sekaligus
                    foreach ($subsequentTransactions as $transaction) {
                        $updatedBudget = $previousDifferent;
                        $updatedDifferent = $updatedBudget - $transaction['actual'];

                        // Update transaksi
                        if (!$realisasiModel->update($transaction['id'], [
                            'budget' => $updatedBudget,
                            'different' => $updatedDifferent,
                            'updated_at' => date('Y-m-d H:i:s'),
                        ])) {
                            log_message('error', 'Gagal memperbarui transaksi ID ' . $transaction['id']);
                            // Tidak menggunakan transaksi, jadi langsung lanjut
                            continue;
                        }

                        // Log pembaruan untuk debugging
                        log_message('info', "Transaksi ID {$transaction['id']} diperbarui: budget={$updatedBudget}, different={$updatedDifferent}");

                        // Set 'previousDifferent' untuk iterasi berikutnya
                        $previousDifferent = $updatedDifferent;
                    }
                } else {
                    log_message('error', 'Realisasi tidak ditemukan untuk formNumber: ' . $formNumber);
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Realisasi tidak ditemukan untuk formNumber yang diberikan'
                    ]);
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Data LPJB berhasil diupdate dan realisasi diperbarui',
                    'debug' => [
                        'formNumber' => $formNumber,
                        'totalRecords' => count($insertData),
                        'totalRealisasi' => $totalRealisasiNumeric
                    ]
                ]);
            } catch (\Exception $e) {
                log_message('error', 'Update error: ' . $e->getMessage());

                // Pastikan data lama dikembalikan jika terjadi error
                if (!empty($existingData)) {
                    foreach ($existingData as $oldData) {
                        $lpjbModel->insert($oldData);
                    }
                }

                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Gagal mengupdate data: ' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in updateLpjb: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteLpjb()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        try {
            $formNumber = $this->request->getPost('formNumber');

            // Debug log
            log_message('debug', 'Attempting to delete LPJB with formNumber: ' . $formNumber);

            if (empty($formNumber)) {
                log_message('error', 'Form Number is empty');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Form Number tidak ditemukan'
                ]);
            }

            $lpjbModel = new LpjbModel();

            // Cek apakah data exists
            $existingData = $lpjbModel->where('formNumber', $formNumber)->findAll();
            if (empty($existingData)) {
                log_message('error', 'No data found for formNumber: ' . $formNumber);
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Data tidak ditemukan'
                ]);
            }

            // Debug log jumlah data yang akan dihapus
            log_message('debug', 'Found ' . count($existingData) . ' records to delete');

            try {
                // Hapus data berdasarkan formNumber
                $deleteResult = $lpjbModel->where('formNumber', $formNumber)->delete();

                // Debug log hasil delete
                log_message('debug', 'Delete result: ' . json_encode($deleteResult));

                if ($deleteResult) {
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'Data berhasil dihapus',
                        'debug' => [
                            'formNumber' => $formNumber,
                            'recordsDeleted' => count($existingData)
                        ]
                    ]);
                } else {
                    log_message('error', 'Failed to delete. Model errors: ' . json_encode($lpjbModel->errors()));
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Gagal menghapus data: ' . json_encode($lpjbModel->errors())
                    ]);
                }
            } catch (\Exception $e) {
                log_message('error', 'Database error during delete: ' . $e->getMessage());
                throw $e;
            }
        } catch (\Exception $e) {
            log_message('error', 'Error in deleteLpjb: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    public function sendRkapNotification()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request'
            ]);
        }

        try {
            $year = $this->request->getPost('year');
            if (empty($year)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Tahun harus dipilih terlebih dahulu',
                    'csrfHash' => csrf_hash()
                ]);
            }

            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            // Ambil data RKAP
            $rkapData = $this->rkapModel->getRkapsByYearUnitDepartments($year, $unit, $department);

            if (empty($rkapData)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Tidak ada data RKAP untuk tahun ' . $year,
                    'csrfHash' => csrf_hash()
                ]);
            }

            // Siapkan data untuk email
            $emailData = [
                'year' => $year,
                'unit' => $unit,
                'department' => $department,
                'total_items' => count($rkapData),
                'total_budget' => array_sum(array_column($rkapData, 'total')),
                'created_by' => session()->get('name'),
                'created_date' => date('Y-m-d H:i:s'),
                'rkap_data' => $rkapData
            ];

            // Konfigurasi email
            $config = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.humi.co.id',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'Hits@2524114#',
                'SMTPPort' => 587,
                'SMTPCrypto' => 'tls',
                'mailType' => 'html',
                'charset' => 'utf-8',
                'wordWrap' => true,
                'validate' => true,
                'SMTPTimeout' => 60
            ];

            $email = \Config\Services::email($config);
            $email->setFrom('<EMAIL>', 'HALO - HUMI');
            $email->setTo('<EMAIL>');
            $email->setBCC('<EMAIL>');
            $email->setSubject("Notifikasi RKAP {$department} - {$year} Siap Review");

            // Siapkan template email
            $message = view('emails/rkap_notification', $emailData);
            $email->setMessage($message);

            // Debug log sebelum mengirim
            log_message('debug', 'Attempting to send email...');
            log_message('debug', 'Email configuration: ' . json_encode($config));

            try {
                $sent = $email->send(false); // Parameter false untuk mendapatkan error details
                $debugInfo = $email->printDebugger(['headers', 'subject', 'body']);

                log_message('debug', 'Email debug info: ' . $debugInfo);

                if ($sent) {
                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Notifikasi RKAP berhasil dikirim',
                        'recipients' => $recipients,
                        'csrfHash' => csrf_hash()
                    ]);
                } else {
                    throw new \RuntimeException('Gagal mengirim email: ' . $debugInfo);
                }
            } catch (\Exception $emailError) {
                log_message('error', 'SMTP Error: ' . $emailError->getMessage());
                throw new \RuntimeException('Gagal mengirim email: ' . $emailError->getMessage());
            }
        } catch (\Exception $e) {
            log_message('error', 'Error sending RKAP notification: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Gagal mengirim notifikasi: ' . $e->getMessage(),
                'debug' => ENVIRONMENT === 'development' ? $e->getTraceAsString() : null,
                'csrfHash' => csrf_hash()
            ]);
        }
    }

    public function getAnggaranAwal()
    {
        if ($this->request->isAJAX()) {
            $acc_id = $this->request->getPost('acc_id');
            $year = $this->request->getPost('year');
            $unit = $this->request->getPost('unit');
            $department = $this->request->getPost('department');

            try {
                $anggaran = $this->rkapModel->where([
                    'id' => $acc_id,
                    'year' => $year,
                    'unit' => $unit,
                    'department' => $department
                ])->first();

                return $this->response->setJSON([
                    'status' => 'success',
                    'anggaran_awal' => $anggaran ? $anggaran['total'] : 0
                ]);
            } catch (\Exception $e) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Failed to fetch data'
                ]);
            }
        }
        return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request']);
    }
    public function getAnggaranAwals()
    {
        if ($this->request->isAJAX()) {
            $data = $this->request->getJSON();

            try {
                $anggaranAwal = $this->rkapModel->getTotalByAccId(
                    $data->acc_id,
                    $data->year,
                    $data->unit,
                    $data->department
                );

                return $this->response->setJSON([
                    'status' => 'success',
                    'anggaran_awal' => $anggaranAwal
                ]);
            } catch (\Exception $e) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Gagal mengambil data anggaran awal: ' . $e->getMessage()
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Invalid request'
        ]);
    }

    public function signature()
    {
        $data['title'] = 'Signature Management';

        // Ambil data dari session
        $unit = session()->get('unit_kerja');
        $department = session()->get('department');

        // Ambil signature yang sesuai dengan unit dan department
        $data['signature'] = $this->signatureModel->getSignatureByUnitDepartment($unit, $department);

        // Cek apakah data signature sudah ada
        $data['isExisting'] = !empty($data['signature']);

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/signature', $data)
            . view('budgeting/partials/footer');
    }

    public function saveSignature()
    {
        if (!$this->request->isAJAX()) {
            log_message('error', 'Non-AJAX request received');
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method'
            ])->setStatusCode(400);
        }

        try {
            // Debug: Log received data
            log_message('debug', 'Received POST data: ' . json_encode($this->request->getPost()));

            // Ambil data dari form
            $data = [
                'diajukan_nama' => $this->request->getPost('diajukan_nama'),
                'diajukan_jabatan' => $this->request->getPost('diajukan_jabatan'),
                'verifikasi_nama' => $this->request->getPost('verifikasi_nama'),
                'verifikasi_jabatan' => $this->request->getPost('verifikasi_jabatan'),
                'disetujui_nama' => $this->request->getPost('disetujui_nama'),
                'disetujui_jabatan' => $this->request->getPost('disetujui_jabatan'),
                'unit' => session()->get('unit_kerja'),
                'department' => session()->get('department'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Debug: Log processed data
            log_message('debug', 'Processed data: ' . json_encode($data));

            // Validasi data
            if (empty($data['unit']) || empty($data['department'])) {
                log_message('error', 'Missing unit or department');
                throw new \Exception('Unit kerja dan department harus diisi');
            }

            // Debug: Log session data
            log_message('debug', 'Session data - Unit: ' . session()->get('unit_kerja') . ', Department: ' . session()->get('department'));

            // Pastikan SignatureModel sudah diinisialisasi
            if (!isset($this->signatureModel)) {
                $this->signatureModel = new \App\Models\SignatureModel();
            }

            // Cek apakah data sudah ada
            $existingSignature = $this->signatureModel->getSignatureByUnitDepartment($data['unit'], $data['department']);

            if ($existingSignature) {
                // Debug: Log update attempt
                log_message('debug', 'Updating existing signature with ID: ' . $existingSignature['id']);

                // Update data yang ada
                if (!$this->signatureModel->update($existingSignature['id'], $data)) {
                    log_message('error', 'Update failed. Model errors: ' . json_encode($this->signatureModel->errors()));
                    throw new \Exception('Gagal memperbarui data signature: ' . implode(', ', $this->signatureModel->errors()));
                }
                $message = 'Data tanda tangan berhasil diperbarui';
            } else {
                // Debug: Log insert attempt
                log_message('debug', 'Inserting new signature');

                // Tambah data baru
                if (!$this->signatureModel->insert($data)) {
                    log_message('error', 'Insert failed. Model errors: ' . json_encode($this->signatureModel->errors()));
                    throw new \Exception('Gagal menambahkan data signature: ' . implode(', ', $this->signatureModel->errors()));
                }
                $message = 'Data tanda tangan berhasil ditambahkan';
            }

            // Debug: Log success
            log_message('debug', 'Operation successful: ' . $message);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => $message,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Signature] Error: ' . $e->getMessage());
            log_message('error', '[Signature] Stack trace: ' . $e->getTraceAsString());

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'debug' => ENVIRONMENT === 'development' ? [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ] : null
            ])->setStatusCode(500);
        }
    }

    public function diterimaFinance()
    {
        $rkapModel = new RkapModel();
        $session = session();
        $department = $session->get('department'); // Mendapatkan departemen dari sesi

        // Mengambil opsi acc_no berdasarkan departemen
        $accNos = $rkapModel->where('department', $department)
            ->findAllAccNos(); // Mengambil acc_no dan description

        // Mengambil opsi tahun
        $yearOptions = $this->realisasiModel->select('YEAR(tanggal) as tahun')
            ->distinct()
            ->orderBy('tahun', 'DESC')
            ->findAll();

        $yearOptionsFormatted = [];
        foreach ($yearOptions as $year) {
            $yearOptionsFormatted[] = ['tahun' => $year['tahun']];
        }

        // Mengambil data realisasi berdasarkan filter
        $earliestBudget = $this->realisasiModel->getEarliestBudgetByYearAndAccNo(date('Y'), array_column($accNos, 'acc_no'));

        $data['title'] = 'Diterima Finance';
        $data['realisasi'] = $this->realisasiModel->findAll();
        $data['earliestBudget'] = $earliestBudget;

        $data['rkapModel'] = $rkapModel; // Kirim model ke view
        $data['accNoOptions'] = $accNos; // Opsi acc_no dengan description
        $data['yearOptions'] = $yearOptionsFormatted; // Opsi tahun

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/diterima-finance', $data)
            . view('budgeting/partials/footer');
    }

    // Helper function for truncating text with tooltip
    private function truncateWithTooltip($text, $maxLength = 30)
    {
        $escaped = esc($text);
        if (strlen($escaped) > $maxLength) {
            return '<span data-bs-toggle="tooltip" title="' . $escaped . '">' .
                substr($escaped, 0, $maxLength) . '...</span>';
        }
        return $escaped;
    }

    public function getDiterimaFinanceData()
    {
        if ($this->request->isAJAX()) {
            try {
                // Get filter parameters from DataTable request
                $paymentStatusFilter = $this->request->getPost('payment_status') ?? '';

                $year = date('Y');
                $session = session();
                $unit = $session->get('unit_kerja');
                $jenis_realisasi = '';

                $results = $this->realisasiModel->getRealisasiAllUnit($year, $unit, $jenis_realisasi);                // Apply payment status filter if specified
                if (!empty($paymentStatusFilter) && $paymentStatusFilter !== 'ALL') {
                    $results = array_filter($results, function ($row) use ($paymentStatusFilter) {
                        $hasDocument = !empty($row['document_finance']) && trim($row['document_finance']) !== '';
                        $isFullPayment = $row['is_full_payment'] == 1;
                        
                        if ($paymentStatusFilter === 'PAID') {
                            return $hasDocument && $isFullPayment;
                        } elseif ($paymentStatusFilter === 'PARTIAL') {
                            return $hasDocument && !$isFullPayment;
                        } elseif ($paymentStatusFilter === 'UNPAID') {
                            return !$hasDocument;
                        }
                        return true; // Show all if filter is 'ALL' or empty
                    });
                }

                // Log jumlah data yang diambil
                log_message('info', 'getRealisasiData: Jumlah data yang diambil = ' . count($results));

                // Format data untuk DataTables
                $data = [];
                $no = 1;
                foreach ($results as $row) {

                    $difference = esc($row['different']);
                    $differenceDisplay = $difference < 0 ?
                        '<span class="text-danger">-' . number_format(abs($difference), 0, ',', '.') . '</span>' :
                        number_format($difference, 0, ',', '.');

                    // Tambahkan tombol preview dokumen jika ada
                    $previewButton = '';
                    if (!empty($row['document_finance'])) {
                        $previewButton = '<a href="javascript:void(0);" 
                            class="avatar-text avatar-md preview-existing-doc" 
                            data-document="' . esc($row['document_finance']) . '" 
                            data-bs-toggle="tooltip" 
                            title="' . count(explode(',', $row['document_finance'])) . ' dokumen terupload">
                            <i class="fas fa-file-pdf"></i>
                        </a>';
                    }

                    // Format action buttons - only detail and verification buttons in main action column
                    $actions = '<div class="hstack gap-2">' .
                        '<a href="javascript:void(0);" class="avatar-text avatar-md detail-btn" ' .
                        'data-id="' . esc($row['id_realisasi']) . '" ' .
                        'data-bs-toggle="tooltip" title="Detail">' .
                        '<i class="fas fa-eye"></i></a>' .

                        $previewButton .

                        '<a href="javascript:void(0);" ' .
                        'class="avatar-text avatar-md verify-btn ' . (esc($row['diterimaFinance']) ? 'verified disabled' : '') . '" ' .
                        'data-id="' . esc($row['id_realisasi']) . '" ' .
                        'data-status="' . (esc($row['diterimaFinance']) ? '1' : '0') . '" ' .
                        'data-verification-date="' . (esc($row['diterimaFinance']) ? date('d/m/Y H:i:s', strtotime($row['diterimaFinance'])) : '') . '" ' .
                        'data-bs-toggle="tooltip" ' .
                        'title="' . (esc($row['diterimaFinance']) ? 'Sudah Diverifikasi<br>' . date('d/m/Y H:i:s', strtotime($row['diterimaFinance'])) : 'Verify') . '">' .
                        '<i class="fas ' . (esc($row['diterimaFinance']) ? 'fa-check-circle text-success' : 'fa-check text-primary') . '"></i></a>' .
                        '</div>';

                    // Create separate upload finance column
                    $uploadFinanceActions = '<div class="hstack gap-2">' .
                        '<a href="javascript:void(0);" class="avatar-text avatar-md upload-finance-btn" ' .
                        'data-id="' . esc($row['id_realisasi']) . '" ' .
                        'data-bs-toggle="tooltip" title="Upload Finance Document">' .
                        '<i class="fas fa-upload"></i></a>' .
                        '</div>';                    // Determine payment status based on document_finance and is_full_payment fields
                    $paymentStatus = 'UNPAID';
                    $paymentBadge = '<span class="badge bg-danger">UNPAID</span>';

                    if (!empty($row['document_finance']) && trim($row['document_finance']) !== '') {
                        if ($row['is_full_payment'] == 1) {
                            $paymentStatus = 'PAID';
                            $paymentBadge = '<span class="badge bg-success">PAID</span>';
                        } else {
                            $paymentStatus = 'PARTIAL';
                            $paymentBadge = '<span class="badge bg-warning">PARTIAL</span>';
                        }
                    }

                    $data[] = [
                        'no' => $no++,
                        'actions' => $actions,
                        'uploadFinance' => $uploadFinanceActions,
                        'paymentStatus' => $paymentBadge,
                        'paymentStatusRaw' => $paymentStatus, // For filtering and export
                        'namaVendor' => $this->truncateWithTooltip($row['namaVendor'] ?? '', 25),
                        'namaVendorRaw' => esc($row['namaVendor'] ?? ''), // Raw text for export
                        'noInvoice' => $this->truncateWithTooltip($row['noInvoice'] ?? '', 20),
                        'noInvoiceRaw' => esc($row['noInvoice'] ?? ''), // Raw text for export
                        'formNumber' => esc($row['formNumber']),
                        'tanggal' => date('d/m/Y', strtotime(esc($row['tanggal']))),
                        'deskripsi' => esc($row['deskripsi']),
                        'actual' => number_format(esc($row['actual']), 0, ',', '.'),
                        'acc_no' => esc($row['acc_no']),
                        'department' => esc($row['department']),
                        // Keep these for other functionality that might need them
                        'id_realisasi' => esc($row['id_realisasi']),
                        'unit' => esc($row['unit']),
                        'user' => esc($row['user']),
                        'description_acc' => esc($row['description_acc']),
                        'difference' => $differenceDisplay,
                        'catatan' => esc($row['catatan'])
                    ];
                }

                // Kembalikan data dalam format JSON
                return $this->response->setJSON(['data' => $data]);
            } catch (\Exception $e) {
                log_message('error', 'Error in getDiterimaFinanceData: ' . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan saat mengambil data'
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid'
        ]);
    }

    public function verifyFinance()
    {
        if ($this->request->isAJAX()) {
            try {
                $id = $this->request->getPost('id');

                // Update status verifikasi dengan format datetime Y-m-d H:i:s
                $updateData = [
                    'diterimaFinance' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $success = $this->realisasiModel->update($id, $updateData);

                if ($success) {
                    // Ambil data yang baru diupdate untuk konfirmasi
                    $updatedData = $this->realisasiModel->find($id);

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Data berhasil diverifikasi pada ' . date('d/m/Y H:i:s', strtotime($updateData['diterimaFinance'])),
                        'verificationDate' => $updateData['diterimaFinance'],
                        'data' => $updatedData,
                        'csrfHash' => csrf_hash()
                    ]);
                } else {
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Gagal memperbarui status verifikasi',
                        'csrfHash' => csrf_hash()
                    ]);
                }
            } catch (\Exception $e) {
                log_message('error', 'Verifikasi error: ' . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                    'csrfHash' => csrf_hash()
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid',
            'csrfHash' => csrf_hash()
        ]);
    }

    public function previewDocument($filename)
    {
        $path = FCPATH . 'uploads/pum_doc/' . $filename;

        if (!file_exists($path)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        // Baca file
        $content = file_get_contents($path);

        // Set header untuk preview saja
        header('Content-Type: application/pdf');
        header('Content-Length: ' . strlen($content));
        header('Cache-Control: inline');
        header('Content-Disposition: inline; filename="' . $filename . '"');
        header('Accept-Ranges: none');

        // Output content
        echo $content;
        exit;
    }

    public function previewDocumentInvoice($filename)
    {
        try {
            // Tambahkan validasi filename
            $filename = basename($filename);
            if (!preg_match('/^[\w\-\.]+$/', $filename)) {
                throw new \Exception('Nama file tidak valid');
            }

            $path = WRITEPATH . 'uploads/invoices/' . $filename;

            // Debug path file
            log_message('debug', 'Mencoba mengakses file di: ' . $path);

            // Gunakan realpath untuk resolving symbolic links
            $realPath = realpath($path);
            if (!$realPath || !is_file($realPath)) {
                log_message('error', 'File tidak ditemukan: ' . $path);
                throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
            }

            // Cek permission
            if (!is_readable($realPath)) {
                log_message('error', 'Permission denied untuk file: ' . $realPath);
                throw new \RuntimeException('Akses file ditolak');
            }

            // Gunakan helper file CI4
            helper('filesystem');

            // Baca file
            $mime = mime_content_type($realPath);
            header('Content-Type: ' . $mime);
            header('Content-Length: ' . filesize($realPath));
            header('Cache-Control: public, must-revalidate, max-age=0');
            header('Pragma: public');
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($realPath)) . ' GMT');
            readfile($realPath);
            exit;
        } catch (\Exception $e) {
            log_message('error', 'Error preview dokumen: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setBody($e->getMessage());
        }
    }

    public function getAccNoByYear()
    {
        if ($this->request->isAJAX()) {
            $year = $this->request->getPost('year');
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            // Ambil acc_no yang memiliki transaksi di tahun tersebut
            $accNos = $this->rkapModel->where('year', $year)
                ->where('unit', $unit)
                ->where('department', $department)
                ->select('*')
                ->distinct()
                ->findAll();

            return $this->response->setJSON($accNos);
        }

        return $this->response->setJSON(['error' => 'Invalid request'], 400);
    }

    public function requestEnableEditing()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request']);
        }

        try {
            $year = date('Y');
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');
            $requestedBy = session()->get('nama');

            // Validasi data sebelum enkripsi
            if (empty($year) || empty($unit) || empty($department) || empty($requestedBy)) {
                throw new \Exception(
                    'Data tidak lengkap: ' .
                        (!$year ? 'tahun, ' : '') .
                        (!$unit ? 'unit, ' : '') .
                        (!$department ? 'department, ' : '') .
                        (!$requestedBy ? 'nama pemohon' : '')
                );
            }

            // Log data sebelum enkripsi
            log_message('debug', "[RKAP Enable] Data before encryption: " . json_encode([
                'year' => $year,
                'unit' => $unit,
                'department' => $department,
                'requested_by' => $requestedBy
            ]));

            // Enkripsi parameter
            $dataToEncrypt = [
                'year' => $year,
                'unit' => $unit,
                'department' => $department,
                'requested_by' => $requestedBy
            ];

            $encryptedData = base64_encode(json_encode($dataToEncrypt));

            // URL dengan data terenkripsi
            $confirmUrl = base_url("budget/confirmEnableEditing?data={$encryptedData}");

            // Konfigurasi email
            $config = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.humi.co.id',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'Hits@2524114#',
                'SMTPPort' => 587,
                'SMTPCrypto' => 'tls',
                'mailType' => 'html',
                'charset' => 'utf-8',
                'wordWrap' => true,
                'validate' => true,
                'SMTPTimeout' => 60
            ];

            $email = \Config\Services::email($config);
            $email->setFrom('<EMAIL>', 'HALO - HUMI');
            $email->setTo('<EMAIL>');
            $email->setBCC('<EMAIL>');
            $email->setSubject("Permintaan Enable Editing RKAP - {$department}");

            $emailContent = "
                <h2>Permintaan Enable Editing RKAP</h2>
                <p>Terdapat permintaan untuk mengaktifkan editing RKAP dengan detail:</p>
                <ul>
                    <li>Department: {$department}</li>
                    <li>Unit: {$unit}</li>
                    <li>Tahun: {$year}</li>
                    <li>Diminta oleh: {$requestedBy}</li>
                    <li>Waktu permintaan: " . date('d/m/Y H:i:s') . "</li>
                </ul>
                <p>Untuk mengaktifkan editing RKAP, silakan klik tombol di bawah ini:</p>
                <p style='margin: 20px 0;'>
                    <a href='{$confirmUrl}' style='
                        background-color: #4CAF50;
                        border: none;
                        color: white;
                        padding: 15px 32px;
                        text-align: center;
                        text-decoration: none;
                        display: inline-block;
                        font-size: 16px;
                        margin: 4px 2px;
                        cursor: pointer;
                        border-radius: 4px;
                    '>
                        Aktifkan Editing RKAP
                    </a>
                </p>
            ";

            $email->setMessage($emailContent);

            // Debug log sebelum mengirim email
            log_message('debug', "[RKAP Enable] Attempting to send email with config: " . json_encode([
                'protocol' => $config['protocol'],
                'SMTPHost' => $config['SMTPHost'],
                'SMTPUser' => $config['SMTPUser'],
                'SMTPPort' => $config['SMTPPort']
            ]));

            if ($email->send(false)) {
                log_message('info', "[RKAP Enable] Email request sent successfully");

                return $this->response->setJSON([
                    'status' => 'success',
                    'message' => 'Permintaan enable editing telah dikirim ke admin',
                    'details' => [
                        'department' => $department,
                        'unit' => $unit,
                        'year' => $year,
                        'requested_at' => date('Y-m-d H:i:s'),
                        'requested_by' => $requestedBy,
                        'email_sent_to' => '<EMAIL>'
                    ]
                ]);
            } else {
                $debugger = $email->printDebugger(['headers', 'subject', 'body']);
                log_message('error', "[RKAP Enable] Failed to send email. Debug info: " . json_encode($debugger));
                throw new \RuntimeException('Gagal mengirim email konfirmasi. Error: ' . implode("\n", (array)$debugger));
            }
        } catch (\Exception $e) {
            log_message('error', "[RKAP Enable] Error: " . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Gagal mengirim permintaan: ' . $e->getMessage()
            ]);
        }
    }

    public function confirmEnableEditing()
    {
        try {
            $encryptedData = $this->request->getGet('data');

            if (empty($encryptedData)) {
                throw new \Exception('Parameter tidak valid');
            }

            // Dekripsi dan validasi data
            $decodedData = base64_decode($encryptedData);
            if ($decodedData === false) {
                throw new \Exception('Gagal mendekripsi data');
            }

            $data = json_decode($decodedData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Data JSON tidak valid: ' . json_last_error_msg());
            }

            // Log data hasil dekripsi
            log_message('debug', "[RKAP Enable] Decoded data: " . json_encode($data));

            // Validasi kelengkapan data
            if (
                !isset($data['year']) || !isset($data['unit']) ||
                !isset($data['department']) || !isset($data['requested_by'])
            ) {
                throw new \Exception('Data tidak lengkap setelah dekripsi');
            }

            $year = $data['year'];
            $unit = $data['unit'];
            $department = $data['department'];
            $requestedBy = $data['requested_by'];

            // Validasi nilai data
            if (empty($year) || empty($unit) || empty($department) || empty($requestedBy)) {
                throw new \Exception(
                    'Ada data yang kosong: ' .
                        (empty($year) ? 'tahun, ' : '') .
                        (empty($unit) ? 'unit, ' : '') .
                        (empty($department) ? 'department, ' : '') .
                        (empty($requestedBy) ? 'nama pemohon' : '')
                );
            }

            // Update schedule di database
            $result = $this->rkapModel->where([
                'year' => $year,
                'unit' => $unit,
                'department' => $department
            ])->set([
                'schedule' => date('Y-m-d H:i:s')
            ])->update();

            if ($result) {
                log_message('info', "[RKAP Enable] Schedule updated successfully for {$department} - {$unit}");

                // Format tanggal Indonesia
                setlocale(LC_TIME, 'id_ID');
                $waktuAktivasi = strftime('%d %B %Y %H:%M:%S', strtotime(date('Y-m-d H:i:s')));

                // Kirim email notifikasi ke department
                $config = [
                    'protocol' => 'smtp',
                    'SMTPHost' => 'mail.humi.co.id',
                    'SMTPUser' => '<EMAIL>',
                    'SMTPPass' => 'Hits@2524114#',
                    'SMTPPort' => 587,
                    'SMTPCrypto' => 'tls',
                    'mailType' => 'html',
                    'charset' => 'utf-8',
                    'wordWrap' => true,
                    'validate' => true,
                    'SMTPTimeout' => 60
                ];

                $email = \Config\Services::email($config);
                $email->setFrom('<EMAIL>', 'HALO - HUMI');

                // Ambil email department dari database berdasarkan department
                $departmentEmail = $this->getUserEmailByDepartment($department);
                $email->setTo($departmentEmail);
                $email->setSubject("Konfirmasi Aktivasi Editing RKAP - {$department}");

                $emailContent = "
                    <h2>Konfirmasi Aktivasi Editing RKAP</h2>
                    <p>Permintaan untuk mengaktifkan editing RKAP telah disetujui dengan detail:</p>
                    <ul>
                        <li>Department: {$department}</li>
                        <li>Unit: {$unit}</li>
                        <li>Tahun: {$year}</li>
                        <li>Diminta oleh: {$requestedBy}</li>
                        <li>Waktu aktivasi: {$waktuAktivasi}</li>
                    </ul>
                    <p>Anda sekarang dapat melakukan edit dan hapus pada data RKAP.</p>
                    <p>Silakan akses menu RKAP melalui link berikut:</p>
                    <p><a href='" . base_url('budget/form-rkap') . "' style='
                        background-color: #4CAF50;
                        border: none;
                        color: white;
                        padding: 15px 32px;
                        text-align: center;
                        text-decoration: none;
                        display: inline-block;
                        font-size: 16px;
                        margin: 4px 2px;
                        cursor: pointer;
                        border-radius: 4px;
                    '>Akses RKAP</a></p>
                ";

                $email->setMessage($emailContent);

                if ($email->send()) {
                    log_message('info', "[RKAP Enable] Confirmation email sent to department: {$department}");
                } else {
                    log_message('error', "[RKAP Enable] Failed to send confirmation email to department: {$department}");
                }

                return redirect()->to('/budget/form-rkap')
                    ->with('success', 'Editing RKAP berhasil diaktifkan untuk ' . $department);
            } else {
                throw new \Exception('Gagal mengupdate status RKAP');
            }
        } catch (\Exception $e) {
            log_message('error', "[RKAP Enable] Confirmation error: " . $e->getMessage());
            return redirect()->to('/budget/form-rkap')
                ->with('error', 'Gagal mengaktifkan editing: ' . $e->getMessage());
        }
    }

    // Fungsi helper untuk mendapatkan email department
    private function getUserEmailByDepartment($department)
    {
        // Ambil email dari user yang meminta (dari session)
        $email = session()->get('email');

        if (empty($email)) {
            log_message('error', "[RKAP Enable] No email found for department: {$department}");
            return '<EMAIL>'; // Email default jika tidak ditemukan
        }

        return $email;
    }    public function uploadFinance()
    {
        try {
            log_message('info', '[Upload Finance] Starting document upload process');

            $file = $this->request->getFile('document');
            $invoiceId = $this->request->getPost('id_realisasi');
            $isFullPayment = $this->request->getPost('is_full_payment') ? 1 : 0; // Get payment status

            log_message('debug', '[Upload Finance] Request data:', [
                'post_data' => $this->request->getPost(),
                'files' => $this->request->getFiles(),
                'is_full_payment' => $isFullPayment
            ]);

            // Validasi ID
            if (empty($invoiceId)) {
                log_message('error', '[Upload Finance] Invoice ID is empty');
                throw new \Exception('ID realisasi tidak valid.');
            }

            // Cek file lama
            $realisasi = $this->realisasiModel->find($invoiceId);
            if ($realisasi && !empty($realisasi['document_finance'])) {
                $oldFile = WRITEPATH . 'uploads/invoices/' . $realisasi['document_finance'];
                log_message('info', '[Upload Finance] Old file exists: ' . $oldFile);
            }

            if (!$file->isValid() || $file->hasMoved()) {
                log_message('error', '[Upload Finance] Invalid file or already moved');
                throw new \Exception('Dokumen gagal diunggah.');
            }

            // Validasi tipe file
            $allowedMimeTypes = [
                'application/pdf',
                'image/jpeg',
                'image/jpg',
                'image/png'
            ];

            if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
                log_message('error', '[Upload Finance] Invalid file type: ' . $file->getMimeType());
                throw new \Exception('Jenis file tidak diizinkan.');
            }

            $uploadPath = WRITEPATH . 'uploads/invoices/';

            // Kompresi file jika bertipe gambar dan ukuran lebih dari 1MB
            if (strpos($file->getMimeType(), 'image/') === 0 && $file->getSize() > 1 * 1024 * 1024) {
                try {
                    log_message('info', '[Upload Finance] Starting image compression');
                    $image = \Config\Services::image();
                    $image->withFile($file->getTempName())
                        ->resize(1920, 1080, true, 'height')
                        ->save($file->getTempName());
                    log_message('info', '[Upload Finance] Image compression completed');
                } catch (\Exception $e) {
                    log_message('error', '[Upload Finance] Image compression failed: ' . $e->getMessage());
                    throw new \Exception('Gagal mengompresi gambar: ' . $e->getMessage());
                }
            }

            $newName = $file->getRandomName();
            log_message('debug', '[Upload Finance] New filename generated: ' . $newName);

            if ($file->move($uploadPath, $newName)) {
                log_message('info', '[Upload Finance] File successfully moved to: ' . $uploadPath . $newName);

                // Update database dengan where clause - include payment status
                $existingFiles = $realisasi['document_finance'] ? explode(',', $realisasi['document_finance']) : [];
                array_push($existingFiles, $newName);
                $combinedFiles = implode(',', $existingFiles);

                // Update both document_finance and is_full_payment fields
                $updateData = [
                    'document_finance' => $combinedFiles,
                    'is_full_payment' => $isFullPayment
                ];

                $result = $this->realisasiModel->where('id', $invoiceId)
                    ->set($updateData)
                    ->update();
                
                // Pilih model dan field berdasarkan sumber
                $model = new RealisasiModel();

                // Ambil data sebelum update
                $originalData = $model->find($invoiceId);
                if (!$originalData) {
                    throw new \Exception('Data tidak ditemukan');
                }

                $source = 'realisasi';
                // Kirim email notifikasi dengan payment status context
                $emailSent = $this->sendNotificationEmail($originalData, $newName, $source, $isFullPayment);

                if ($result) {
                    log_message('info', '[Upload Finance] Database updated successfully for invoice ID: ' . $invoiceId . ' with payment status: ' . ($isFullPayment ? 'FULL' : 'PARTIAL'));

                    $paymentStatusText = $isFullPayment ? 'Full Payment' : 'Partial Payment';
                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Dokumen berhasil diupload sebagai ' . $paymentStatusText . ' & email terkirim ke ' . $originalData['department'],
                        'files' => $combinedFiles,
                        'payment_status' => $paymentStatusText
                    ]);
                } else {
                    log_message('error', '[Upload Finance] Database update failed for invoice ID: ' . $invoiceId);
                    // Hapus file baru jika update database gagal
                    unlink($uploadPath . $newName);
                    throw new \Exception('Gagal memperbarui informasi dokumen di database');
                }
            } else {
                log_message('error', '[Upload Finance] Failed to move uploaded file');
                throw new \Exception('Gagal memindahkan file.');
            }
        } catch (\Exception $e) {
            log_message('error', '[Upload Finance] Error: ' . $e->getMessage());
            log_message('error', '[Upload Finance] Stack trace: ' . $e->getTraceAsString());

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }    private function sendNotificationEmail($invoice, $documentName, $source = 'invoice', $isFullPayment = 0)
    {
        try {
            // Validasi data invoice
            if (!$invoice || !is_array($invoice)) {
                log_message('error', 'Data invoice tidak valid: ' . json_encode($invoice));
                return false;
            }

            // Log data invoice untuk debugging
            log_message('info', 'Data invoice yang diterima: ' . json_encode($invoice));

            // Sesuaikan field berdasarkan sumber data
            if ($source === 'realisasi') {
                $requiredFields = [
                    'id',
                    'department',
                    'noInvoice',
                    'namaVendor',
                    'tanggal',      // Realisasi menggunakan 'tanggal'
                    'actual',       // Realisasi menggunakan 'actual'
                    'deskripsi'     // Realisasi menggunakan 'deskripsi'
                ];
            } else {
                $requiredFields = [
                    'id',
                    'department',
                    'noInvoice',
                    'namaVendor',
                    'tanggalInvoice',   // Invoice menggunakan 'tanggalInvoice'
                    'jumlahTagihan',    // Invoice menggunakan 'jumlahTagihan'
                    'perihal'           // Invoice menggunakan 'perihal'
                ];
            }

            foreach ($requiredFields as $field) {
                if (!isset($invoice[$field])) {
                    log_message('error', "Field {$field} tidak ditemukan dalam data {$source}");
                    return false;
                }
            }

            $departmentModel = new DepartmentModel();
            $department = $invoice['department'];

            // Ambil email departemen
            $dept = $departmentModel->where('department', $department)->first();
            if (!$dept || empty($dept['email'])) {
                log_message('error', "Email untuk departemen {$department} tidak ditemukan.");
                return false;
            }
            $receiverEmail = $dept['email'];            // Buat link download langsung
            $downloadLink = base_url('uploads/invoices/' . $documentName);
            
            // Determine payment status text and styling
            $paymentStatusText = $isFullPayment ? 'Full Payment' : 'Partial Payment';
            $paymentStatusStyle = $isFullPayment ? 'color: #28a745; font-weight: bold;' : 'color: #ffc107; font-weight: bold;';

            // Prepare data based on source type
            if ($source === 'realisasi') {
                $tanggalField = date('d F Y', strtotime($invoice['tanggal']));
                $jumlahField = 'Rp. ' . number_format($invoice['actual'], 2, ',', '.');
                $perihalField = $invoice['deskripsi'];
            } else {
                $tanggalField = date('d F Y', strtotime($invoice['tanggalInvoice']));
                $jumlahField = 'Rp. ' . number_format($invoice['jumlahTagihan'], 2, ',', '.');
                $perihalField = $invoice['perihal'];
            }

            // Siapkan isi email dengan link download
            $subject = "Notifikasi Upload Bukti Pembayaran - " . ($source === 'realisasi' ? 'Realisasi' : 'Invoice') . " {$invoice['noInvoice']} ({$paymentStatusText})";
            $body = "
                <h3>Dokumen Telah Diupload</h3>
                <p>Dokumen baru telah diupload dengan detail sebagai berikut:</p>
                <table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>
                    <tr style='background-color: #f8f9fa;'>
                        <th style='padding: 12px; border: 1px solid #dee2e6;'>No Invoice</th>
                        <th style='padding: 12px; border: 1px solid #dee2e6;'>Vendor</th>
                        <th style='padding: 12px; border: 1px solid #dee2e6;'>Tanggal Invoice</th>
                        <th style='padding: 12px; border: 1px solid #dee2e6;'>Jumlah " . ($source === 'realisasi' ? 'Actual' : 'Tagihan') . "</th>
                        <th style='padding: 12px; border: 1px solid #dee2e6;'>Status Pembayaran</th>
                        <th style='padding: 12px; border: 1px solid #dee2e6;'>" . ($source === 'realisasi' ? 'Deskripsi' : 'Perihal') . "</th>
                    </tr>
                    <tr>
                        <td style='padding: 12px; border: 1px solid #dee2e6;'>{$invoice['noInvoice']}</td>
                        <td style='padding: 12px; border: 1px solid #dee2e6;'>{$invoice['namaVendor']}</td>
                        <td style='padding: 12px; border: 1px solid #dee2e6;'>{$tanggalField}</td>
                        <td style='padding: 12px; border: 1px solid #dee2e6;'>{$jumlahField}</td>
                        <td style='padding: 12px; border: 1px solid #dee2e6; {$paymentStatusStyle}'>{$paymentStatusText}</td>
                        <td style='padding: 12px; border: 1px solid #dee2e6;'>{$perihalField}</td>
                    </tr>
                </table>
                <p><strong>Nama File:</strong> {$documentName}</p>
                <p><strong>Jenis Dokumen:</strong> " . ($source === 'realisasi' ? 'Dokumen Realisasi' : 'Dokumen Invoice') . "</p>
                <p><strong>Status Pembayaran:</strong> <span style='{$paymentStatusStyle}'>{$paymentStatusText}</span></p>
                <p><strong>Link Download:</strong> <a href='{$downloadLink}'>Klik di sini untuk mengunduh dokumen</a></p>
                <br>
                <p>Email ini dikirim secara otomatis, mohon tidak membalas email ini.</p>
            ";

            // Konfigurasi PHPMailer
            $mail = new PHPMailer(true);
            try {
                // Server settings
                $mail->isSMTP();
                $mail->Host       = config('Email')->SMTPHost;
                $mail->SMTPAuth   = true;
                $mail->Username   = config('Email')->SMTPUser;
                $mail->Password   = config('Email')->SMTPPass;
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                $mail->Port       = config('Email')->SMTPPort;

                // Recipients
                $mail->setFrom('<EMAIL>', 'HALO - HUMI');
                $mail->addAddress($receiverEmail);
                $mail->addBCC('<EMAIL>');

                // Content
                $mail->isHTML(true);
                $mail->Subject = $subject;
                $mail->Body    = $body;

                $mail->send();
                log_message('info', "Notifikasi email terkirim ke: {$receiverEmail} untuk " .
                    ($source === 'realisasi' ? 'Realisasi' : 'Invoice') . " ID: {$invoice['id']}");

                return true;
            } catch (Exception $e) {
                log_message('error', "Email tidak dapat dikirim ke: {$receiverEmail}. Kesalahan: {$mail->ErrorInfo}");
                return false;
            }
        } catch (\Exception $e) {
            log_message('error', "Kesalahan saat mengirim email: " . $e->getMessage() . "\nData invoice: " . json_encode($invoice));
            return false;
        }
    }

    public function buat_akun()
    {
        $data['title'] = 'Buat Akun';
        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/buat_akun', $data)
            . view('budgeting/partials/footer');
    }
    public function verifyGiftmoney()
    {
        if ($this->request->isAJAX()) {
            try {
                $id = $this->request->getPost('id');

                // Validasi ID
                if (empty($id)) {
                    throw new \Exception('ID tidak valid');
                }

                // Log sebelum update
                log_message('debug', "[RKAP Giftmoney] Attempting to verify ID: {$id}");

                // Update status verifikasi dengan format datetime Y-m-d H:i:s
                $updateData = [
                    'verifGiftmoney' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $success = $this->realisasiModel->update($id, $updateData);

                if ($success) {
                    // Ambil data yang baru diupdate untuk konfirmasi
                    $updatedData = $this->realisasiModel->find($id);

                    // Kirim email notifikasi
                    $this->sendGiftmoneyVerificationEmail($updatedData);

                    log_message('info', "[RKAP Giftmoney] Successfully verified ID: {$id}");

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Data telah diverifikasi pada ' . date('d/m/Y H:i:s', strtotime($updateData['verifGiftmoney'])),
                        'verificationDate' => $updateData['verifGiftmoney'],
                        'data' => $updatedData,
                        'csrfHash' => csrf_hash()
                    ]);
                } else {
                    log_message('error', "[RKAP Giftmoney] Failed to update database for ID: {$id}");
                    throw new \Exception('Gagal memperbarui status verifikasi');
                }
            } catch (\Exception $e) {
                log_message('error', "[RKAP Giftmoney] Error: " . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'csrfHash' => csrf_hash()
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid',
            'csrfHash' => csrf_hash()
        ]);
    }

    private function sendGiftmoneyVerificationEmail($realisasiData)
    {
        try {
            // Cek apakah form number mengandung /PUM/
            if (strpos($realisasiData['formNumber'], '/PUM/') === false) {
                log_message('info', "Skip email verifikasi giftmoney untuk non-PUM: {$realisasiData['formNumber']}");
                return false;
            }

            $unitModel = new UnitModel();
            $unitData = $unitModel->where('unit', $realisasiData['unit'])->first();

            $departmentModel = new DepartmentModel();
            $department = $departmentModel->where('department', $realisasiData['department'])
                ->where('unit', $unitData['id'] ?? null)
                ->first();

            if (!$department || empty($department['email'])) {
                log_message('error', "Email department tidak ditemukan untuk: {$realisasiData['department']}");
                return false;
            }

            $config = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.humi.co.id',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'Hits@2524114#',
                'SMTPPort' => 587,
                'SMTPCrypto' => 'tls',
                'mailType' => 'html',
                'charset' => 'utf-8',
                'wordWrap' => true
            ];


            $email = \Config\Services::email($config);
            $email->setFrom('<EMAIL>', 'HALO - HUMI');
            $email->setTo($department['email']);
            $email->setBCC('<EMAIL>');
            $email->setSubject("Informasi PUM - {$realisasiData['formNumber']} Uang Diserahkan");

            $emailContent = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
                    <h2 style="color: #2c3e50; margin: 0;">
                        <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                        Uang Fisik PUM Telah Diserahkan
                    </h2>
                </div>

                <div style="padding: 25px; background-color: #ffffff;">
                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px;">
                            Detail Transaksi PUM
                        </h3>
                        
                        <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                            <tr>
                                <td style="width: 35%; padding: 12px 10px; background-color: #f8f9fa;">Nomor PUM</td>
                                <td style="width: 65%; padding: 12px 10px;">' . $realisasiData['formNumber'] . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Tanggal Serah</td>
                                <td style="padding: 12px 10px;">' . date('d/m/Y H:i', strtotime($realisasiData['verifGiftmoney'])) . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Jumlah</td>
                                <td style="padding: 12px 10px; font-weight: bold; color: #28a745;">
                                    Rp ' . number_format($realisasiData['actual'], 0, ',', '.') . '
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px;">
                            Penerima
                        </h3>
                        
                        <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                            <tr>
                                <td style="width: 35%; padding: 12px 10px; background-color: #f8f9fa;">Department</td>
                                <td style="width: 65%; padding: 12px 10px;">' . $realisasiData['department'] . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Unit</td>
                                <td style="padding: 12px 10px;">' . $realisasiData['unit'] . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Pemohon</td>
                                <td style="padding: 12px 10px;">' . $realisasiData['user'] . '</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px 10px; background-color: #f8f9fa;">Diterima Oleh</td>
                                <td style="padding: 12px 10px;">' . $realisasiData['namaVendor'] . '</td>
                            </tr>
                        </table>
                    </div>

                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;">
                        <h4 style="color: #856404; margin: 0 0 10px 0;">Deskripsi Transaksi:</h4>
                        <p style="margin: 0; color: #856404;">' . $realisasiData['deskripsi'] . '</p>
                    </div>

                    <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; text-align: center;">
                        <p style="margin: 0; color: #6c757d; font-size: 0.9em;">
                            <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                            Email ini merupakan konfirmasi otomatis bahwa uang fisik PUM telah diserahkan ke department terkait.
                            <br>Mohon tidak membalas email ini.
                        </p>
                    </div>
                </div>
            </div>';

            $email->setMessage($emailContent);

            if (!$email->send()) {
                log_message('error', "Gagal mengirim email verifikasi giftmoney ke: {$department['email']}");
                return false;
            }

            log_message('info', "Email verifikasi giftmoney terkirim ke: {$department['email']}");
            return true;
        } catch (\Exception $e) {
            log_message('error', "Error sending giftmoney verification email: " . $e->getMessage());
            return false;
        }
    }

    public function verifAccmoney()
    {
        if ($this->request->isAJAX()) {
            try {
                $id = $this->request->getPost('id');

                // Validasi ID
                if (empty($id)) {
                    throw new \Exception('ID tidak valid');
                }

                // Update status verifikasi dengan format datetime Y-m-d H:i:s
                $updateData = [
                    'verifAccmoney' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $success = $this->realisasiModel->update($id, $updateData);

                if ($success) {
                    // Ambil data yang baru diupdate untuk konfirmasi
                    $updatedData = $this->realisasiModel->find($id);

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Data telah diverifikasi pada ' . date('d/m/Y H:i:s', strtotime($updateData['verifAccmoney'])),
                        'verificationDate' => $updateData['verifAccmoney'],
                        'data' => $updatedData,
                        'csrfHash' => csrf_hash()
                    ]);
                } else {
                    throw new \Exception('Gagal memperbarui status verifikasi');
                }
            } catch (\Exception $e) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'csrfHash' => csrf_hash()
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid',
            'csrfHash' => csrf_hash()
        ]);
    }

    public function verifEndmoneyuser()
    {
        if ($this->request->isAJAX()) {
            try {
                $id = $this->request->getPost('id');

                // Validasi ID
                if (empty($id)) {
                    throw new \Exception('ID tidak valid');
                }

                // Log sebelum update
                log_message('debug', "[RKAP Endmoney] Attempting to verify ID: {$id}");

                // Update status verifikasi dengan format datetime Y-m-d H:i:s
                $updateData = [
                    'verifEndmoney' => 'User ' . date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $success = $this->realisasiModel->update($id, $updateData);

                if ($success) {
                    // Ambil data yang baru diupdate untuk konfirmasi
                    $updatedData = $this->realisasiModel->find($id);

                    log_message('info', "[RKAP Endmoney] Successfully verified ID: {$id}");

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Data telah diverifikasi pada ' . date('d/m/Y H:i:s', strtotime(substr($updateData['verifEndmoney'], 5))), // Memotong 'User ' dari awal string
                        'verificationDate' => $updateData['verifEndmoney'],
                        'data' => $updatedData,
                        'csrfHash' => csrf_hash()
                    ]);
                } else {
                    log_message('error', "[RKAP Endmoney] Failed to update database for ID: {$id}");
                    throw new \Exception('Gagal memperbarui status verifikasi');
                }
            } catch (\Exception $e) {
                log_message('error', "[RKAP Endmoney] Error: " . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'csrfHash' => csrf_hash()
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid',
            'csrfHash' => csrf_hash()
        ]);
    }

    public function verifEndmoneyfinance()
    {
        if ($this->request->isAJAX()) {
            try {
                $id = $this->request->getPost('id');

                // Validasi ID
                if (empty($id)) {
                    throw new \Exception('ID tidak valid');
                }

                // Log sebelum update
                log_message('debug', "[RKAP Endmoney] Attempting to verify ID: {$id}");

                // Update status verifikasi dengan format datetime Y-m-d H:i:s
                $updateData = [
                    'verifEndmoney' => 'Finance ' . date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $success = $this->realisasiModel->update($id, $updateData);

                if ($success) {
                    // Ambil data yang baru diupdate untuk konfirmasi
                    $updatedData = $this->realisasiModel->find($id);

                    log_message('info', "[RKAP Endmoney] Successfully verified ID: {$id}");

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Data telah diverifikasi pada ' . date('d/m/Y H:i:s', strtotime(substr($updateData['verifEndmoney'], 8))), // Memotong 'User ' dari awal string
                        'verificationDate' => $updateData['verifEndmoney'],
                        'data' => $updatedData,
                        'csrfHash' => csrf_hash()
                    ]);
                } else {
                    log_message('error', "[RKAP Endmoney] Failed to update database for ID: {$id}");
                    throw new \Exception('Gagal memperbarui status verifikasi');
                }
            } catch (\Exception $e) {
                log_message('error', "[RKAP Endmoney] Error: " . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'csrfHash' => csrf_hash()
                ]);
            }
        }

        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Permintaan tidak valid',
            'csrfHash' => csrf_hash()
        ]);
    }

    public function checkPUMEligibility()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request']);
        }

        try {
            $data = $this->request->getJSON();
            $department = $data->department ?? '';
            $unit = $data->unit ?? '';

            // 1. Ambil formNumber yang bertuliskan PUM sesuai unit dan department
            $existingPUM = $this->realisasiModel->where([
                'department' => $department,
                'unit' => $unit,
            ])
                ->where("formNumber LIKE '%/PUM/%'")
                ->get()
                ->getResultArray();

            if (!empty($existingPUM)) {
                $problemPUMs = [];
                foreach ($existingPUM as $pum) {
                    $issues = [];

                    // 2. Check apakah verifGiftmoney NULL atau tidak
                    if (!empty($pum['verifGiftmoney'])) {
                        // 3. Check apakah sudah lebih dari 14 hari
                        $verifGiftDate = new DateTime($pum['verifGiftmoney']);
                        $now = new DateTime();
                        $interval = $verifGiftDate->diff($now);

                        // Jika sudah lebih dari 14 hari sejak verifGiftmoney
                        if ($interval->days >= 14) {
                            // 4. Check apakah verifAccmoney tidak NULL
                            if (!empty($pum['verifAccmoney'])) {
                                // 5. Check apakah verifEndmoney bertuliskan Finance saja
                                // Abaikan jika verifEndmoney kosong, atau dimulai dengan 'User'
                                if ($pum['verifEndmoney'] === 'Finance') {
                                    $issues[] = "sisa uang belum diterima & diverifikasi oleh finance";
                                }
                            }

                            // Check LPJB jika belum diupload
                            if (empty($pum['tanggal_upload_lpjb'])) {
                                $issues[] = "belum upload LPJB selama {$interval->days} hari";
                            }
                        }
                    }

                    // Jika ada masalah, tambahkan ke daftar PUM bermasalah
                    if (!empty($issues)) {
                        $problemPUMs[] = [
                            'formNumber' => $pum['formNumber'],
                            'issues' => $issues
                        ];
                    }
                }

                if (!empty($problemPUMs)) {
                    $message = "<strong>PUM tidak tersedia karena:</strong><br>";
                    foreach ($problemPUMs as $pum) {
                        $message .= "• Form {$pum['formNumber']} " . implode(' dan ', $pum['issues']) . "<br>";
                    }
                    $message .= "<br>Silakan selesaikan proses PUM yang ada terlebih dahulu.";

                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => $message
                    ]);
                }
            }

            return $this->response->setJSON(['status' => 'success']);
        } catch (\Exception $e) {
            log_message('error', 'Error checking PUM eligibility: ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat memeriksa eligibilitas PUM'
            ]);
        }
    }

    public function salaryBudget()
    {
        $data['title'] = 'Salary Budget';
        $data['budgeting'] = $this->budgetingModel->findAll();

        return view('budgeting/partials/header', $data)
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/salary-budget', $data)
            . view('budgeting/partials/footer');
    }

    public function saveSalaryBudget()
    {
        $model = new SalaryBudgetModel();

        // Ambil data dari form
        $data = [
            'nama_perusahaan' => $this->request->getPost('nama_perusahaan'),
            'deskripsi' => $this->request->getPost('deskripsi'),
            'gaji_tunjangan' => $this->request->getPost('gaji_tunjangan'),
            'pph21_atas_gajitunjangan' => $this->request->getPost('pph21'),
            'biaya_tenaga_ahli' => $this->request->getPost('biaya_tenaga_ahli'), // Tambahkan ini
            'iuran_kopkar' => $this->request->getPost('iuran_kopkar'),
            'total_gajitunjangan' => $this->request->getPost('total_gaji_tunjangan'),
            'dplk_6' => $this->request->getPost('dplk_6'),
            'dplk_3' => $this->request->getPost('dplk_3'),
            'ditransfer_pada' => $this->request->getPost('transfer_date'),
            'unit' => session()->get('unit_kerja'),
            'department' => session()->get('department'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Proses field tambahan
        $labels = $this->request->getPost('tambahan_label[]') ?? [];
        $nilais = $this->request->getPost('tambahan_nilai[]') ?? [];

        // Validasi maksimal 5 field tambahan
        $totalFields = min(count($labels), 5);

        // Inisialisasi semua section dan field dengan nilai default
        for ($i = 0; $i < 5; $i++) {
            $sectionNumber = $i + 1;
            $data["section_$sectionNumber"] = '';
            $data["field_$sectionNumber"] = 0;
        }

        // Isi data yang diinput user
        for ($i = 0; $i < $totalFields; $i++) {
            $sectionNumber = $i + 1;
            $data["section_$sectionNumber"] = $labels[$i] ?? '';
            $data["field_$sectionNumber"] = $nilais[$i] ?? 0;
        }

        try {
            if ($model->save($data)) { // Gunakan save() untuk memanfaatkan validasi model
                $successMessage = 'Data budget untuk ' . $data['deskripsi'] . ' berhasil disimpan!';
                return redirect()->back()->with('success', $successMessage);
            }

            // Jika validasi gagal, ambil error dari model
            return redirect()->back()
                ->withInput()
                ->with('validation_errors', $model->errors())
                ->with('error', 'Data gagal disimpan! Periksa kembali inputan Anda');
        } catch (\Exception $e) {
            $errorMessage = 'Gagal menyimpan data: ' . $e->getMessage();
            return redirect()->back()
                ->withInput()
                ->with('error', $errorMessage);
        }
    }

    public function autocompleteGaji()
    {
        if ($this->request->isAJAX()) {
            $term = $this->request->getGet('term');
            $realisasiModel = new RealisasiModel();
            $results = $realisasiModel->getDeskripsiGaji($term);

            $data = [];
            foreach ($results as $row) {
                $data[] = [
                    'label' => $row['deskripsi'] . ' - ' . $row['formNumber'] . ' - Rp' . number_format($row['actual'], 0, ',', '.'),
                    'value' => $row['actual'],
                    'formNumber' => $row['formNumber']
                ];
            }

            return $this->response->setJSON($data);
        }
        return $this->response->setJSON(['error' => 'Permintaan tidak valid.'], 400);
    }

    public function recordSalaryBudget()
    {
        $model = new SalaryBudgetModel();
        $session = session();
        $department = $session->get('department');
        $unit = $session->get('unit_kerja');

        // Ambil data salary budget
        $data['salaryBudgets'] = $model->where('department', $department)
            ->where('unit', $unit)
            ->orderBy('created_at', 'DESC')
            ->findAll();

        $data['title'] = 'Riwayat Budget Gaji';

        return view('budgeting/partials/header', $data)
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/record-salary', $data)
            . view('budgeting/partials/footer');
    }

    public function getSalaryBudget($id)
    {
        $this->response->setContentType('application/json');
        $data = $this->salaryBudgetModel->getSalaryBudgetById($id);
        return $this->response->setJSON($data);
    }

    public function updateSalaryBudget()
    {
        $validation = \Config\Services::validation();
        $validation->setRules([
            'nama_perusahaan' => 'required',
            'gaji_tunjangan' => 'required',
            'pph21_atas_gajitunjangan' => 'required',
            'iuran_kopkar' => 'required',
            'dplk_6' => 'required',
            'dplk_3' => 'required',
            'ditransfer_pada' => 'required|valid_date'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $validation->getErrors()
            ]);
        }

        // Konversi format currency ke angka
        $data = [
            'id' => $this->request->getPost('id'),
            'nama_perusahaan' => $this->request->getPost('nama_perusahaan'),
            'deskripsi' => $this->request->getPost('deskripsi'),
            'gaji_tunjangan' => (float) str_replace('.', '', $this->request->getPost('gaji_tunjangan')),
            'pph21_atas_gajitunjangan' => (float) str_replace('.', '', $this->request->getPost('pph21_atas_gajitunjangan')),
            'iuran_kopkar' => (float) str_replace('.', '', $this->request->getPost('iuran_kopkar')),
            'biaya_tenaga_ahli' => (float) str_replace('.', '', $this->request->getPost('biaya_tenaga_ahli')),
            'dplk_6' => (float) str_replace('.', '', $this->request->getPost('dplk_6')),
            'dplk_3' => (float) str_replace('.', '', $this->request->getPost('dplk_3')),
            'total_gajitunjangan' => (float) str_replace('.', '', $this->request->getPost('total_gajitunjangan')),
            'ditransfer_pada' => $this->request->getPost('ditransfer_pada')
        ];

        // Tambahkan section dan field 1-5
        for ($i = 1; $i <= 5; $i++) {
            $data["section_$i"] = $this->request->getPost("section_$i");
            $data["field_$i"] = (float) str_replace('.', '', $this->request->getPost("field_$i"));
        }

        if ($this->salaryBudgetModel->saveSalaryBudget($data)) {
            return $this->response->setJSON([
                'status' => true,
                'message' => 'Data berhasil diperbarui'
            ]);
        } else {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Gagal memperbarui data'
            ]);
        }
    }

    public function deleteSalaryBudget()
    {
        $id = $this->request->getPost('id');
        log_message('debug', "Attempting to delete salary budget ID: {$id}");

        $delete = $this->salaryBudgetModel->deleteSalaryBudget($id);

        log_message('debug', "Delete result: " . ($delete ? 'success' : 'failed'));

        if ($delete) {
            return $this->response->setJSON([
                'status' => true,
                'message' => 'Data berhasil dihapus'
            ]);
        } else {
            log_message('error', "Failed to delete ID: {$id}. Error: " . $this->db->error());
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Gagal menghapus data. Error: ' . $this->db->error()
            ]);
        }
    }

    public function signatureSalary()
    {
        $data['title'] = 'Signature Salary Management';

        // Ambil data dari session
        $unit = session()->get('unit_kerja');
        $department = session()->get('department');

        // Ambil model
        $signatureModel = new \App\Models\SignatureSalaryModel();

        // Ambil data signature
        $data['signature'] = $signatureModel->where('unit', $unit)
            ->where('department', $department)
            ->first();
        // Cek apakah data sudah ada
        $data['isExisting'] = !empty($data['signature']);

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/signature-salary', $data)
            . view('budgeting/partials/footer');
    }

    public function saveSignatureSalary()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method'
            ])->setStatusCode(400);
        }

        try {
            // Ambil data dari session
            $unit = session()->get('unit_kerja');
            $department = session()->get('department');

            // Validasi data
            $data = [
                'dibuat_nama' => $this->request->getPost('dibuat_nama'),
                'dibuat_jabatan' => $this->request->getPost('dibuat_jabatan'),
                'diketahui_nama' => $this->request->getPost('diketahui_nama'),
                'diketahui_jabatan' => $this->request->getPost('diketahui_jabatan'),
                'disetujui_nama' => $this->request->getPost('disetujui_nama'),
                'disetujui_jabatan' => $this->request->getPost('disetujui_jabatan'),
                'unit' => $unit,
                'department' => $department,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $model = new \App\Models\SignatureSalaryModel();

            // Cek data existing
            $existing = $model->where('unit', $unit)
                ->where('department', $department)
                ->first();

            if ($existing) {
                // Update data
                $model->update($existing['id'], $data);
                $message = 'Data tanda tangan berhasil diperbarui';
            } else {
                // Insert baru
                $model->insert($data);
                $message = 'Data tanda tangan berhasil disimpan';
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Fungsi untuk autocomplete vendor
     */
    public function autocompleteVendor()
    {
        if ($this->request->isAJAX()) {
            $term = $this->request->getVar('term');

            $vendorModel = new \App\Models\VendorModel();
            $vendors = $vendorModel->searchVendors($term);

            return $this->response->setJSON($vendors);
        }

        return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request']);
    }

    /**
     * Fungsi untuk mendapatkan detail vendor berdasarkan nama
     */
    public function getVendorDetails()
    {
        if ($this->request->isAJAX()) {
            $vendorName = $this->request->getVar('vendor_name');

            $vendorModel = new \App\Models\VendorModel();
            $vendor = $vendorModel->getVendorByName($vendorName);

            if ($vendor) {
                return $this->response->setJSON([
                    'status' => 'success',
                    'data' => $vendor
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Vendor tidak ditemukan'
                ]);
            }
        }

        return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request']);
    }

    public function signatureLpjb()
    {
        $data['title'] = 'Signature Management';

        // Ambil data dari session
        $unit = session()->get('unit_kerja');
        $department = session()->get('department');

        // Ambil signature yang sesuai dengan unit dan department
        $data['signature'] = $this->signatureLpjbModel->getSignatureByUnitDepartment($unit, $department);

        // Cek apakah data signature sudah ada
        $data['isExisting'] = !empty($data['signature']);

        return view('budgeting/partials/header')
            . view('budgeting/partials/sidebar', $data)
            . view('budgeting/partials/topbar', $data)
            . view('budgeting/signature-lpjb', $data)
            . view('budgeting/partials/footer');
    }

    public function saveSignatureLpjb()
    {
        if (!$this->request->isAJAX()) {
            log_message('error', 'Non-AJAX request received');
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method'
            ])->setStatusCode(400);
        }

        try {
            // Debug: Log received data
            log_message('debug', 'Received POST data: ' . json_encode($this->request->getPost()));

            // Ambil data dari form
            $data = [
                'diajukan_nama' => $this->request->getPost('diajukan_nama'),
                'diajukan_jabatan' => $this->request->getPost('diajukan_jabatan'),
                'disetujui_nama' => $this->request->getPost('disetujui_nama'),
                'disetujui_jabatan' => $this->request->getPost('disetujui_jabatan'),
                'unit' => session()->get('unit_kerja'),
                'department' => session()->get('department'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Debug: Log processed data
            log_message('debug', 'Processed data: ' . json_encode($data));

            // Validasi data
            if (empty($data['unit']) || empty($data['department'])) {
                log_message('error', 'Missing unit or department');
                throw new \Exception('Unit kerja dan department harus diisi');
            }

            // Debug: Log session data
            log_message('debug', 'Session data - Unit: ' . session()->get('unit_kerja') . ', Department: ' . session()->get('department'));

            // Pastikan SignatureModel sudah diinisialisasi
            if (!isset($this->signatureLpjbModel)) {
                $this->signatureLpjbModel = new \App\Models\SignatureLpjbModel();
            }

            // Cek apakah data sudah ada
            $existingSignature = $this->signatureLpjbModel->getSignatureByUnitDepartment($data['unit'], $data['department']);

            if ($existingSignature) {
                // Debug: Log update attempt
                log_message('debug', 'Updating existing signature with ID: ' . $existingSignature['id']);

                // Update data yang ada
                if (!$this->signatureLpjbModel->update($existingSignature['id'], $data)) {
                    log_message('error', 'Update failed. Model errors: ' . json_encode($this->signatureLpjbModel->errors()));
                    throw new \Exception('Gagal memperbarui data signature: ' . implode(', ', $this->signatureLpjbModel->errors()));
                }
                $message = 'Data tanda tangan berhasil diperbarui';
            } else {
                // Debug: Log insert attempt
                log_message('debug', 'Inserting new signature');

                // Tambah data baru
                if (!$this->signatureLpjbModel->insert($data)) {
                    log_message('error', 'Insert failed. Model errors: ' . json_encode($this->signatureLpjbModel->errors()));
                    throw new \Exception('Gagal menambahkan data signature: ' . implode(', ', $this->signatureLpjbModel->errors()));
                }
                $message = 'Data tanda tangan berhasil ditambahkan';
            }

            // Debug: Log success
            log_message('debug', 'Operation successful: ' . $message);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => $message,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Signature LPJB] Error: ' . $e->getMessage());
            log_message('error', '[Signature LPJB] Stack trace: ' . $e->getTraceAsString());

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'debug' => ENVIRONMENT === 'development' ? [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ] : null
            ])->setStatusCode(500);
        }
    }
    public function deleteFinanceDocument()
    {
        // Set header Content-Type untuk memastikan response JSON
        $this->response->setContentType('application/json');

        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method'
            ])->setStatusCode(400);
        }

        try {
            $id = $this->request->getPost('id');
            $filename = $this->request->getPost('filename');

            // Validasi input
            if (empty($id) || empty($filename)) {
                throw new \Exception('ID atau nama file tidak valid');
            }

            // Log proses delete
            log_message('info', '[Delete Finance Doc] Starting delete process for ID: ' . $id . ', File: ' . $filename);

            // Ambil data realisasi
            $realisasi = $this->realisasiModel->find($id);
            if (!$realisasi) {
                throw new \Exception('Data realisasi tidak ditemukan');
            }

            // Parse dokumen yang ada
            $existingFiles = !empty($realisasi['document_finance']) ? explode(',', $realisasi['document_finance']) : [];
            $originalFileCount = count($existingFiles);

            // Log file yang ada sebelum dihapus
            log_message('debug', '[Delete Finance Doc] Existing files: ' . json_encode($existingFiles));

            // Cek apakah file yang akan dihapus ada dalam daftar
            if (!in_array(trim($filename), array_map('trim', $existingFiles))) {
                throw new \Exception('File yang akan dihapus tidak ditemukan dalam database');
            }

            // Hapus file dari array
            $updatedFiles = array_filter($existingFiles, function ($file) use ($filename) {
                return trim($file) !== trim($filename);
            });

            $newFileCount = count($updatedFiles);

            // Log perubahan
            log_message('debug', '[Delete Finance Doc] Files after removal: ' . json_encode($updatedFiles));
            log_message('info', '[Delete Finance Doc] File count: ' . $originalFileCount . ' -> ' . $newFileCount);

            // Update database
            $newDocumentString = !empty($updatedFiles) ? implode(',', $updatedFiles) : null;

            $updateResult = $this->realisasiModel->update($id, [
                'document_finance' => $newDocumentString
            ]);

            if (!$updateResult) {
                throw new \Exception('Gagal mengupdate database');
            }

            // Hapus file fisik dari server
            $filePath = WRITEPATH . 'uploads/invoices/' . trim($filename);
            $fileDeleted = false;

            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    $fileDeleted = true;
                    log_message('info', '[Delete Finance Doc] Physical file deleted: ' . $filePath);
                } else {
                    log_message('warning', '[Delete Finance Doc] Failed to delete physical file: ' . $filePath);
                }
            } else {
                log_message('warning', '[Delete Finance Doc] Physical file not found: ' . $filePath);
            }

            // Siapkan pesan sukses yang detail
            $successMessage = 'Dokumen "' . $filename . '" berhasil dihapus dari database';
            if ($fileDeleted) {
                $successMessage .= ' dan server';
            } else {
                $successMessage .= ' (file fisik mungkin sudah tidak ada)';
            }
            log_message('info', '[Delete Finance Doc] Delete process completed successfully');

            // Siapkan response yang konsisten
            $response = [
                'status' => 'success',
                'message' => $successMessage,
                'remaining_files' => $updatedFiles,
                'deleted_file' => $filename,
                'file_count_before' => $originalFileCount,
                'file_count_after' => $newFileCount,
                'physical_file_deleted' => $fileDeleted,
                'timestamp' => date('Y-m-d H:i:s')
            ];

            log_message('debug', '[Delete Finance Doc] Sending response: ' . json_encode($response));

            return $this->response->setJSON($response);
        } catch (\Exception $e) {
            log_message('error', '[Delete Finance Doc] Error: ' . $e->getMessage());
            log_message('error', '[Delete Finance Doc] Stack trace: ' . $e->getTraceAsString());

            $errorResponse = [
                'status' => 'error',
                'message' => 'Gagal menghapus dokumen: ' . $e->getMessage(),
                'error_details' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];

            log_message('debug', '[Delete Finance Doc] Sending error response: ' . json_encode($errorResponse));

            return $this->response->setJSON($errorResponse)->setStatusCode(500);
        }
    }
}
