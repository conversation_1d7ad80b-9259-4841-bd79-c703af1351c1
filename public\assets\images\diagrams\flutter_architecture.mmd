graph TB
    %% Main Application Layer
    subgraph "📱 HUMI Flutter App"
        direction TB
        
        %% Entry Point
        MAIN[main.dart<br/>Entry Point]
        
        %% State Management Layer
        subgraph "🔄 State Management (Provider)"
            AUTH_PROV[AuthProvider<br/>User Authentication]
            FILE_PROV[FileDeltaUpdateProvider<br/>File Updates]
            HYBRID_PROV[HybridUpdateProvider<br/>App Updates]
        end
        
        %% UI Layer
        subgraph "🎨 UI Layer"
            direction LR
            
            subgraph "📱 Screens"
                DASH[Dashboard Screen]
                ATT[Attendance Screen]
                OT[Overtime Screen]
                PROF[Profile Screen]
                FACE[Face Verification]
                CAL[Calendar Screen]
                HIST[History Screen]
            end
            
            subgraph "🧩 Widgets"
                UPDATE_WIDGET[Update Progress Widget]
                DELTA_BANNER[File Delta Banner]
                PERM_WRAPPER[Permission Wrapper]
                EMAIL_AUTO[Email Autocomplete]
                DOWNLOAD_DIALOG[Download Progress]
            end
        end
        
        %% Service Layer
        subgraph "⚙️ Service Layer"
            direction TB
            
            API_SVC[ApiService<br/>HTTP Communication]
            NOTIF_SVC[NotificationService<br/>Firebase Messaging]
            PERM_SVC[PermissionService<br/>Device Permissions]
            UPDATE_SVC[App Update Service]
            DELTA_SVC[Delta Update Service]
            CAL_SVC[Calendar Service]
        end
        
        %% Utility Layer
        subgraph "🛠️ Utils & Helpers"
            AUTH_UTILS[Authentication Utils]
            NAV_HELPER[Navigation Helper]
            COLOR_HELPER[Color Helper]
            CONFIG_HELPER[Config Helper]
            SEC_HELPER[Security Helper]
            IOS_HELPER[iOS Helper]
        end
        
        %% Models
        subgraph "📊 Data Models"
            USER_MODEL[User Model]
            OT_MODEL[Overtime Request Model]
            NOTIF_MODEL[Notification Model]
            UPDATE_MODEL[Hybrid Update Models]
        end
    end
    
    %% External Services
    subgraph "🌐 External Services"
        direction TB
        
        subgraph "🔐 Authentication"
            MS_OAUTH[Microsoft OAuth2<br/>PKCE Flow]
            BIOMETRIC[Biometric Auth<br/>Local Auth]
        end
        
        subgraph "🔥 Firebase Services"
            FB_CORE[Firebase Core]
            FB_MSG[Firebase Messaging]
            FB_STORAGE[Firebase Storage]
        end
        
        subgraph "🗺️ Location & Maps"
            GPS[GPS/Geolocator]
            MAPBOX[Mapbox Maps]
            GEOCODING[Geocoding Service]
        end
        
        subgraph "🤖 AI/ML Services"
            TFLITE[TensorFlow Lite<br/>Face Recognition]
            ML_KIT[Google ML Kit<br/>Face Detection]
            OPENCV[OpenCV Dart<br/>Image Processing]
        end
        
        subgraph "📱 Device Features"
            CAMERA[Camera]
            LOCAL_AUTH[Local Authentication]
            STORAGE[Local Storage]
            PERMISSIONS[Device Permissions]
        end
    end
    
    %% Backend Integration
    subgraph "🖥️ Backend Services"
        HUMI_API[HUMI API<br/>apps.humi.co.id]
        CI_BACKEND[CodeIgniter Backend]
        MYSQL_DB[(MySQL Database)]
    end
    
    %% Connections
    MAIN --> AUTH_PROV
    MAIN --> FILE_PROV
    MAIN --> HYBRID_PROV
    
    AUTH_PROV --> DASH
    AUTH_PROV --> API_SVC
    
    DASH --> ATT
    DASH --> OT
    DASH --> PROF
    DASH --> FACE
    
    ATT --> GPS
    ATT --> CAMERA
    FACE --> TFLITE
    FACE --> ML_KIT
    
    API_SVC --> HUMI_API
    NOTIF_SVC --> FB_MSG
    UPDATE_SVC --> DELTA_SVC
    
    HUMI_API --> CI_BACKEND
    CI_BACKEND --> MYSQL_DB
    
    %% Styling
    classDef mainApp fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef stateManagement fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef uiLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef serviceLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef backend fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class MAIN mainApp
    class AUTH_PROV,FILE_PROV,HYBRID_PROV stateManagement
    class DASH,ATT,OT,PROF,FACE,CAL,HIST,UPDATE_WIDGET,DELTA_BANNER uiLayer
    class API_SVC,NOTIF_SVC,PERM_SVC,UPDATE_SVC,DELTA_SVC serviceLayer
    class MS_OAUTH,FB_CORE,GPS,TFLITE,CAMERA external
    class HUMI_API,CI_BACKEND,MYSQL_DB backend
