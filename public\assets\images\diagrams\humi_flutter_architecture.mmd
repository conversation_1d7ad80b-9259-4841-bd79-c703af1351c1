graph TB
    %% HUMI Flutter Application Architecture - Detailed Analysis
    subgraph "📱 HUMI FLUTTER APPLICATION"
        direction TB

        %% Entry Point & Initialization
        subgraph "🚀 Application Bootstrap"
            MAIN[main.dart<br/>🚀 Entry Point<br/>Firebase Init<br/>Provider Setup<br/>1516 lines]
            INIT[Initialization Flow<br/>🔧 Firebase Core<br/>📱 Notification Service<br/>🔄 Delta Update Check]
        end

        %% State Management Layer
        subgraph "🔄 State Management (Provider Pattern)"
            direction LR
            AUTH_PROV[AuthProvider<br/>👤 User Authentication<br/>Session Management<br/>Token Persistence]
            FILE_PROV[FileDeltaUpdateProvider<br/>📦 File Updates<br/>Delta Patches<br/>Version Control]
            HYBRID_PROV[HybridUpdateProvider<br/>🔄 App Updates<br/>Smart Update Logic<br/>User Preferences]
        end
        
        %% Core UI Layer
        subgraph "🎨 Core UI Screens"
            direction TB
            
            subgraph "🔐 Authentication Flow"
                LOGIN[LoginScreen<br/>Microsoft OAuth2<br/>PKCE Flow]
                LOADING[LoadingScreen<br/>Session Check<br/>Asset Preload]
            end
            
            subgraph "📊 Main Application"
                DASH[DashboardScreen<br/>📈 Main Hub<br/>User Info<br/>Quick Actions]
                WEBVIEW[WebViewScreen<br/>🌐 OAuth Handler<br/>Deep Links]
            end
            
            subgraph "👤 User Management"
                PROFILE[ProfileScreen<br/>👤 User Profile<br/>Settings]
                EDIT_PROF[EditProfileScreen<br/>✏️ Profile Editor]
            end
        end
        
        %% Feature Screens
        subgraph "🏢 Business Features"
            direction TB
            
            subgraph "⏰ Attendance Management"
                ATT[AttendanceScreen<br/>📍 GPS Check-in/out<br/>Location Validation]
                ATT_AMEND[AttendanceAmendmentScreen<br/>📝 Time Corrections<br/>Approval Workflow]
                FACE_REG[FaceRegistrationScreen<br/>🤳 Biometric Setup<br/>ML Model Training]
                FACE_VER[FaceVerificationScreen<br/>🔍 Identity Verification<br/>TensorFlow Lite]
            end
            
            subgraph "💼 Work Management"
                OT[OvertimeRequestScreen<br/>⏱️ Overtime Requests<br/>Multi-level Approval]
                CAL[CalendarScreen<br/>📅 Schedule View<br/>Event Management]
                HIST[HistoryScreen<br/>📋 Activity Log<br/>Status Tracking]
            end
            
            subgraph "📢 Communication"
                NOTICE[CompanyNoticeScreen<br/>📢 Announcements<br/>Company Updates]
                NOTIF[NotificationsScreen<br/>🔔 Push Notifications<br/>Action Items]
                PENDING[PendingApprovalsScreen<br/>✅ Approval Queue<br/>Decision Making]
            end
        end
        
        %% Service Layer
        subgraph "⚙️ Service Layer"
            direction TB
            
            subgraph "🌐 API Communication"
                API_SVC[ApiService<br/>🔗 HTTP Client<br/>HUMI Backend<br/>Microsoft Graph]
                NOTIF_SVC[NotificationService<br/>🔔 Firebase Messaging<br/>Local Notifications]
            end
            
            subgraph "📱 Device Services"
                PERM_SVC[PermissionService<br/>🔒 Device Permissions<br/>Runtime Requests]
                CAL_SVC[CalendarService<br/>📅 Calendar Integration<br/>Event Sync]
            end
            
            subgraph "🔄 Update Services"
                UPDATE_SVC[AppUpdateService<br/>📦 Version Management]
                DELTA_SVC[DeltaUpdateService<br/>🔄 Incremental Updates]
                HYBRID_MGR[HybridUpdateManager<br/>⚡ Smart Updates]
                DOWNLOAD_MGR[DownloadQueueManager<br/>📥 File Management]
            end
        end
        
        %% Widget Components
        subgraph "🧩 Reusable Widgets"
            direction LR
            
            subgraph "🔄 Update Components"
                UPDATE_WIDGET[UpdateProgressWidget<br/>📊 Progress Display]
                DELTA_BANNER[FileDeltaBanner<br/>🏷️ Update Notifications]
                ENHANCED_UI[EnhancedUpdateUI<br/>✨ Modern Update UX]
            end
            
            subgraph "🛡️ Security & Permissions"
                PERM_WRAPPER[PermissionWrapper<br/>🔒 Access Control]
                DOWNLOAD_DIALOG[DownloadProgressDialog<br/>📥 Transfer Status]
            end
            
            subgraph "📝 Input Components"
                EMAIL_AUTO[EmailAutocompleteField<br/>📧 Smart Input]
            end
        end
        
        %% Utility Layer
        subgraph "🛠️ Utility Layer"
            direction TB
            
            subgraph "🔐 Security Utils"
                SEC_HELPER[SecurityHelper<br/>🔐 Encryption<br/>Token Management]
                AUTH_UTILS[AuthenticatorUtils<br/>🔑 2FA Support<br/>TOTP Generation]
                AUTH_POLL[AuthenticationPollingUtils<br/>⏱️ Session Monitoring]
            end
            
            subgraph "🎨 UI Utils"
                COLOR_HELPER[ColorHelper<br/>🎨 Theme Management]
                NAV_HELPER[NavigationHelper<br/>🧭 Route Management]
                CONFIG_HELPER[ConfigHelper<br/>⚙️ App Configuration]
            end
            
            subgraph "📱 Platform Utils"
                IOS_HELPER[IOSHelper<br/>🍎 iOS Specific]
                DYNAMIC_LOADER[DynamicAssetLoader<br/>📦 Asset Management]
                MAPBOX_STYLE[MapboxStyle<br/>🗺️ Map Theming]
            end
        end
    end
    
    %% External Services
    subgraph "🌐 External Services & APIs"
        direction TB
        
        subgraph "🔐 Authentication Services"
            MS_OAUTH[Microsoft OAuth2<br/>🔑 Enterprise SSO<br/>Azure AD Integration]
            MS_GRAPH[Microsoft Graph API<br/>👤 User Profile<br/>Organization Data]
        end
        
        subgraph "🔥 Firebase Ecosystem"
            FB_CORE[Firebase Core<br/>🔥 Platform Init]
            FB_MSG[Firebase Messaging<br/>📱 Push Notifications]
            FB_STORAGE[Firebase Storage<br/>☁️ File Storage]
        end
        
        subgraph "🗺️ Location & Maps"
            GPS[GPS/Geolocator<br/>📍 Location Services]
            GEOCODING[Geocoding Service<br/>🏠 Address Resolution]
            MAPBOX[Mapbox Maps<br/>🗺️ Interactive Maps]
        end
        
        subgraph "🤖 AI/ML Services"
            TFLITE[TensorFlow Lite<br/>🧠 Face Recognition<br/>On-device ML]
            ML_KIT[Google ML Kit<br/>👁️ Face Detection<br/>Computer Vision]
            OPENCV[OpenCV Dart<br/>🖼️ Image Processing]
        end
        
        subgraph "📱 Device Features"
            CAMERA[Camera API<br/>📷 Photo Capture<br/>Video Recording]
            LOCAL_AUTH[Local Authentication<br/>👆 Biometric Auth<br/>PIN/Pattern]
            STORAGE[Local Storage<br/>💾 SharedPreferences<br/>File System]
            CONNECTIVITY[Connectivity Plus<br/>🌐 Network Status]
        end
    end
    
    %% Backend Services
    subgraph "🏢 HUMI Backend Services"
        direction TB
        
        HUMI_API[HUMI REST API<br/>🔗 apps.humi.co.id<br/>Business Logic]
        CI_BACKEND[CodeIgniter 4<br/>⚙️ PHP Backend<br/>MVC Architecture]
        MYSQL_DB[MySQL Database<br/>🗄️ Data Persistence<br/>Relational Storage]
    end
    
    %% Connections - Entry & State Management
    MAIN --> AUTH_PROV
    MAIN --> FILE_PROV
    MAIN --> HYBRID_PROV
    MAIN --> FB_CORE
    
    %% Authentication Flow
    AUTH_PROV --> LOGIN
    LOGIN --> MS_OAUTH
    LOGIN --> WEBVIEW
    WEBVIEW --> MS_GRAPH
    MS_GRAPH --> HUMI_API
    
    %% Main Application Flow
    AUTH_PROV --> LOADING
    LOADING --> DASH
    DASH --> ATT
    DASH --> OT
    DASH --> PROFILE
    DASH --> CAL
    
    %% Feature Connections
    ATT --> GPS
    ATT --> CAMERA
    ATT --> FACE_VER
    FACE_VER --> TFLITE
    FACE_VER --> ML_KIT
    FACE_VER --> OPENCV
    FACE_REG --> CAMERA
    FACE_REG --> TFLITE
    
    %% Service Layer Connections
    API_SVC --> HUMI_API
    NOTIF_SVC --> FB_MSG
    UPDATE_SVC --> DELTA_SVC
    HYBRID_MGR --> DOWNLOAD_MGR
    
    %% Backend Connections
    HUMI_API --> CI_BACKEND
    CI_BACKEND --> MYSQL_DB
    
    %% Widget Integration
    PERM_WRAPPER --> PERM_SVC
    UPDATE_WIDGET --> UPDATE_SVC
    DELTA_BANNER --> FILE_PROV
    
    %% Utility Connections
    SEC_HELPER --> LOCAL_AUTH
    NAV_HELPER --> DASH
    AUTH_UTILS --> MS_OAUTH
    
    %% Styling
    classDef entryPoint fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef stateManagement fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef coreUI fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef businessFeatures fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef services fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef widgets fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef utilities fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef external fill:#fafafa,stroke:#616161,stroke-width:2px
    classDef backend fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    
    class MAIN entryPoint
    class AUTH_PROV,FILE_PROV,HYBRID_PROV stateManagement
    class LOGIN,LOADING,DASH,WEBVIEW,PROFILE,EDIT_PROF coreUI
    class ATT,ATT_AMEND,OT,CAL,HIST,FACE_REG,FACE_VER,NOTICE,NOTIF,PENDING businessFeatures
    class API_SVC,NOTIF_SVC,PERM_SVC,CAL_SVC,UPDATE_SVC,DELTA_SVC,HYBRID_MGR,DOWNLOAD_MGR services
    class UPDATE_WIDGET,DELTA_BANNER,ENHANCED_UI,PERM_WRAPPER,DOWNLOAD_DIALOG,EMAIL_AUTO widgets
    class SEC_HELPER,AUTH_UTILS,AUTH_POLL,COLOR_HELPER,NAV_HELPER,CONFIG_HELPER,IOS_HELPER,DYNAMIC_LOADER,MAPBOX_STYLE utilities
    class MS_OAUTH,MS_GRAPH,FB_CORE,FB_MSG,FB_STORAGE,GPS,GEOCODING,MAPBOX,TFLITE,ML_KIT,OPENCV,CAMERA,LOCAL_AUTH,STORAGE,CONNECTIVITY external
    class HUMI_API,CI_BACKEND,MYSQL_DB backend
