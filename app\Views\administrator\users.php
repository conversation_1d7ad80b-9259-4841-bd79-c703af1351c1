<!--start main wrapper-->
<main class="main-wrapper">
    <div class="main-content">
        <h6 class="mb-0 text-uppercase"><?= $title; ?></h6>
        <hr>
        <div class="card">
            <div class="card-header">
                <div class="text-end m-1 d-flex justify-content-end">
                    <button type="button" class="btn btn-outline-primary px-4 d-flex gap-2" data-bs-toggle="modal" data-bs-target="#addUsersModal">Add User</button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="main-menu" class="table table-striped table-bordered" style="width:100%">
                        <thead>
                            <tr>
                                <th style="width: 10%;">#</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Access Data</th>
                                <th>Status</th>
                                <th>Unit</th>
                                <th>Department</th>
                                <th>Division</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
    <?php $i = 1; ?>
    <?php foreach ($users as $u) : ?>
        <tr>
            <td><?= $i++; ?></td>
            <td><?= $u['nama']; ?></td>
            <td><?= $u['email']; ?></td>
            <td><?= $u['role']; ?></td>
            <td>
                <?php 
                    $colors = ['bg-info', 'bg-success', 'bg-warning', 'bg-danger', 'bg-primary'];
                    if (!empty($u['access_data'])) {
                        foreach ($u['access_data'] as $index => $access) {
                            $colorClass = $colors[$index % count($colors)];
                            echo '<span class="badge ' . $colorClass . ' text-dark m-1">' . implode(', ', $access) . '</span>';
                        }
                    } else {
                        echo '<span class="badge bg-secondary text-dark m-1">No Access</span>';
                    }
                ?>
            </td>
            <td><?= $u['is_active'] == 1 ? 'aktif' : 'tidak aktif'; ?></td>
            <td><?= $u['unit_kerja']; ?></td>
            <td><?= $u['department']; ?></td>
            <td><?= $u['division']; ?></td>
            <td>
                <a href="#" data-bs-toggle="modal" data-bs-target="#editUserModal"
                   data-user-id="<?= $u['id']; ?>"
                   data-nama="<?= $u['nama']; ?>"
                   data-email="<?= $u['email']; ?>"
                   data-nip="<?= $u['nip']; ?>"
                   data-role-id="<?= $u['role_id']; ?>"
                   data-is-active="<?= $u['is_active']; ?>"
                   data-unit-kerja="<?= $u['unit_kerja']; ?>"
                   data-unit-id="<?= $u['unit_id'] ?? ''; ?>"
                   data-inactive-reason="<?= $u['inactive_reason']; ?>"
                   data-inactive-date="<?= $u['inactive_date']; ?>"
                   data-mutasi="<?= $u['mutasi_off']; ?>"
                   data-department="<?= $u['department']; ?>"
                   data-division="<?= $u['division']; ?>"
                   data-microsoft-id="<?= $u['microsoft_id']; ?>">
                    <span class="badge bg-warning">Edit</span>
                </a>
                <a href="#" data-bs-toggle="modal" data-bs-target="#DeleteUserModal"
                   data-user-id="<?= $u['id']; ?>">
                    <span class="badge bg-danger">Hapus</span>
                </a>
            </td>
        </tr>
    <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th style="width: 5%;">#</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Access Data</th>
                                <th>Status</th>
                                <th>Unit</th>
                                <th>Department</th>
                                <th>Division</th>
                                <th>Action</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</main>
<!--end main wrapper-->





<!-- Modal untuk Tambah User -->
<div class="modal fade" id="addUsersModal">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header border-bottom-0 py-2 bg-grd-info">
                <h5 class="modal-title text-white">Tambah User</h5>
                <a href="javascript:;" class="primaery-menu-close" data-bs-dismiss="modal">
                    <i class="material-icons-outlined">close</i>
                </a>
            </div>
            <div class="modal-body">
                <div class="form-body">
                    <form id="addUserForm" class="row g-3">
                        <?= csrf_field() ?>
                        <div class="col-md-12">
                            <label for="add_nama" class="form-label">Name</label>
                            <input type="text" class="form-control" id="add_nama" name="nama" placeholder="Name" required>
                        </div>
                        <div class="col-md-12">
                            <label for="add_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="add_email" name="email" placeholder="Email" required>
                        </div>
                        <div class="col-md-12">
                            <label for="add_nip" class="form-label">NIP</label>
                            <input type="text" class="form-control" id="add_nip" name="nip" placeholder="NIP">
                        </div>
                        <div class="col-md-12">
                            <label for="add_role" class="form-label">Role</label>
                            <select class="form-select" id="add_role" name="role" required>
                                <option value="">Pilih Role</option>
                                <?php foreach ($role as $r): ?>
                                    <option value="<?= $r['id_role'] ?>"><?= ucfirst($r['role']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="add_is_active" class="form-label">Status</label>
                            <select class="form-select" id="add_is_active" name="is_active" required>
                                <option value="1" selected>Aktif</option>
                                <option value="0">Tidak Aktif</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="add_unit_kerja" class="form-label">Unit Kerja</label>
                            <select class="form-select" id="add_unit_kerja" name="unit_kerja" required>
                                <option value="">Pilih Unit</option>
                                <?php foreach ($unit as $u): ?>
                                    <option value="<?= $u['unit'] ?>" data-id="<?= $u['id'] ?>"><?= ucfirst($u['unit']) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <input type="hidden" id="add_unit_id" name="unit_id">
                        </div>
                        <div class="col-md-12">
                            <label for="add_division" class="form-label">Division</label>
                            <select class="form-select" id="add_division" name="division" disabled>
                                <option value="">Pilih Division</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="add_department" class="form-label">Department</label>
                            <select class="form-select" id="add_department" name="department" disabled>
                                <option value="">Pilih Department</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="add_microsoft_id" class="form-label">Microsoft ID</label>
                            <input type="text" class="form-control" id="add_microsoft_id" name="microsoft_id" placeholder="Microsoft ID" required>
                        </div>
                        <div class="col-md-12">
                            <div class="d-md-flex d-grid align-items-center gap-3 justify-content-end">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                <button type="submit" class="btn btn-grd-info text-white px-4">Submit</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk Edit User -->
<div class="modal fade" id="editUserModal">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header border-bottom-0 py-2 bg-warning">
                <h5 class="modal-title text-white">Edit User</h5>
                <a href="javascript:;" class="primaery-menu-close" data-bs-dismiss="modal">
                    <i class="material-icons-outlined">close</i>
                </a>
            </div>
            <div class="modal-body">
                <form id="editUserForm" class="row g-3">
                    <?= csrf_field() ?>
                    <input type="hidden" id="editUserId" name="id" value="">
                    <div class="col-md-12">
                        <label for="edit_nama" class="form-label">Nama</label>
                        <input type="text" class="form-control" id="edit_nama" name="nama" placeholder="Nama" required>
                    </div>
                    <div class="col-md-12">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" placeholder="Email" readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="col-md-12">
                        <label for="edit_nip" class="form-label">NIP</label>
                        <input type="text" class="form-control" id="edit_nip" name="nip" placeholder="NIP">
                    </div>
                    <div class="col-md-12">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="">Pilih Role</option>
                            <?php foreach ($role as $r): ?>
                                <option value="<?= $r['id_role'] ?>"><?= ucfirst($r['role']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-12">
                        <label for="edit_is_active" class="form-label">Status</label>
                        <select class="form-select" id="edit_is_active" name="is_active" required>
                            <option value="1">Aktif</option>
                            <option value="0">Tidak Aktif</option>
                        </select>
                    </div>
                    <div class="col-md-12">
                        <label for="edit_unit_kerja" class="form-label">Unit Kerja</label>
                        <select class="form-select" id="edit_unit_kerja" name="unit_kerja" required>
                            <option value="">Pilih Unit</option>
                            <?php foreach ($unit as $u): ?>
                                <option value="<?= $u['unit'] ?>" data-id="<?= $u['id'] ?>"><?= ucfirst($u['unit']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <input type="hidden" id="edit_unit_id" name="unit_id">
                    </div>
                    <div class="col-md-12">
                        <label for="edit_division" class="form-label">Division</label>
                        <select class="form-select" id="edit_division" name="division" disabled>
                            <option value="">Pilih Division</option>
                        </select>
                    </div>
                    <div class="col-md-12">
                        <label for="edit_department" class="form-label">Department</label>
                        <select class="form-select" id="edit_department" name="department" disabled>
                            <option value="">Pilih Department</option>
                        </select>
                    </div>
                    <div class="form-group" id="inactive_reason_group" style="display: none;">
                        <label for="edit_inactive_reason">Alasan Tidak Aktif</label>
                        <select id="edit_inactive_reason" name="inactive_reason" class="form-control">
                            <option value="">Pilih Alasan</option>
                            <option value="pensiun">Pensiun</option>
                            <option value="Mengundurkan Diri">Mengundurkan Diri</option>
                            <option value="Meninggal Dunia">Meninggal Dunia</option>
                            <option value="Habis Kontrak">Habis Kontrak</option>
                            <option value="Mutasi">Mutasi</option>
                        </select>
                    </div>
                    <div class="form-group" id="mutasi_group" style="display: none;">
                        <label for="edit_mutasi">Mutasi</label>
                        <select id="edit_mutasi" name="mutasi" class="form-control">
                        <option value="">Pilih Mutasi</option>
                            <?php foreach ($unit as $u): ?>
                                <option value="<?= $u['unit'] ?>"><?= ucfirst($u['unit']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group" id="inactive_date_group" style="display: none;">
                        <label for="edit_inactive_date">Tanggal Tidak Aktif</label>
                        <input type="date" id="edit_inactive_date" name="inactive_date" class="form-control">
                    </div>
                    <div class="col-md-12">
                        <label for="edit_microsoft_id" class="form-label">Microsoft ID</label>
                        <input type="text" class="form-control" id="edit_microsoft_id" name="microsoft_id" placeholder="Microsoft ID">
                    </div>
                    <div class="col-md-12">
                        <div class="d-md-flex d-grid align-items-center gap-3 justify-content-end">
                            <button type="submit" class="btn btn-warning text-white px-4">Update</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk Konfirmasi Hapus User -->
<div class="modal fade" id="DeleteUserModal" tabindex="-1" aria-labelledby="DeleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-bottom-0 bg-danger py-2">
                <h5 class="modal-title text-white" id="DeleteUserModalLabel">Hapus User</h5>
                <a href="javascript:;" class="primaery-menu-close" data-bs-dismiss="modal">
                    <i class="material-icons-outlined text-white">close</i>
                </a>
            </div>
            <form id="deleteUserForm" class="row g-3">
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus user ini?</p>
                    <input type="hidden" id="deleteUserId" name="id" value="">
                    <?= csrf_field() ?>
                </div>
                <div class="modal-footer border-top-0">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        $('#edit_is_active').change(function() {
            const isActive = $(this).val();
            if (isActive === '0') { // Jika status tidak aktif
                $('#inactive_reason_group').show();
                $('#inactive_date_group').show();
            } else { // Jika status aktif
                $('#inactive_reason_group').hide();
                $('#inactive_date_group').hide();
                $('#mutasi_group').hide();
            }
        });

        $('#edit_inactive_reason').change(function() {
            const onMutasi = $(this).val();
            if (onMutasi === 'Mutasi') { // Jika status tidak aktif
                $('#mutasi_group').show();
            } else { // Jika status aktif
                $('#mutasi_group').hide();
            }
        });

        // Fungsi untuk mendapatkan dan mengisi dropdown division berdasarkan unit_id
        function loadDivisions(unitId, targetDropdown) {
            if (!unitId) {
                $(targetDropdown).html('<option value="">Pilih Division</option>').prop('disabled', true);
                console.log('Division dropdown disabled - no unit selected');
                return;
            }

            $.ajax({
                url: '<?= base_url('administrator/getDivisionsByUnit'); ?>',
                type: 'GET',
                data: { unit_id: unitId },
                dataType: 'json',
                success: function(response) {
                    console.log('Response dari getDivisionsByUnit:', response);
                    if (response.status === 'success') {
                        let options = '<option value="">Pilih Division</option>';
                        response.data.forEach(function(division) {
                            console.log('Comparing division:', division.id, division.division);
                            options += `<option value="${division.division}" data-id="${division.id}">${division.division}</option>`;
                        });
                        $(targetDropdown).html(options).prop('disabled', false);
                    } else {
                        $(targetDropdown).html('<option value="">Pilih Division</option>').prop('disabled', true);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading divisions:', error);
                    $(targetDropdown).html('<option value="">Error loading divisions</option>').prop('disabled', true);
                }
            });
        }

        // Fungsi untuk mendapatkan dan mengisi dropdown department berdasarkan unit_id
        function loadDepartments(unitId, targetDropdown) {
            if (!unitId) {
                $(targetDropdown).html('<option value="">Pilih Department</option>').prop('disabled', true);
                console.log('Department dropdown disabled - no unit selected');
                return;
            }

            $.ajax({
                url: '<?= base_url('administrator/getDepartmentsByUnit'); ?>',
                type: 'GET',
                data: { unit_id: unitId },
                dataType: 'json',
                success: function(response) {
                    console.log('Response dari getDepartmentsByUnit:', response);
                    if (response.status === 'success') {
                        let options = '<option value="">Pilih Department</option>';
                        response.data.forEach(function(department) {
                            console.log('Comparing department:', department.id, department.department);
                            options += `<option value="${department.department}" data-id="${department.id}">${department.department}</option>`;
                        });
                        $(targetDropdown).html(options).prop('disabled', false);
                    } else {
                        $(targetDropdown).html('<option value="">Pilih Department</option>').prop('disabled', true);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading departments:', error);
                    $(targetDropdown).html('<option value="">Error loading departments</option>').prop('disabled', true);
                }
            });
        }

        // Event handler untuk perubahan unit pada modal tambah user
        $('#add_unit_kerja').change(function() {
            const unitId = $(this).find('option:selected').data('id');
            $('#add_unit_id').val(unitId);
            loadDivisions(unitId, '#add_division');
            loadDepartments(unitId, '#add_department');
        });

        // Event handler untuk perubahan unit pada modal edit user
        $('#edit_unit_kerja').change(function() {
            const unitId = $(this).find('option:selected').data('id');
            const unitName = $(this).val();
            $('#edit_unit_id').val(unitId);
            
            console.log('Unit changed:', unitName, 'Unit ID:', unitId);
            
            // Pastikan dropdown division dan department diaktifkan
            $('#edit_division').prop('disabled', false);
            $('#edit_department').prop('disabled', false);
            console.log('Division and Department dropdowns enabled via change event');
            
            loadDivisions(unitId, '#edit_division');
            loadDepartments(unitId, '#edit_department');
        });

        // Ketika modal edit user dibuka, load division dan department berdasarkan unit
        $('#editUserModal').on('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            
            // Ambil data dari button
            const userId = button.dataset.userId;
            const nama = button.dataset.nama;
            const email = button.dataset.email;
            const nip = button.dataset.nip;
            const roleId = button.dataset.roleId;
            const isActive = button.dataset.isActive;
            const unitKerja = button.dataset.unitKerja;
            const unitId = button.dataset.unitId;
            const inactiveReason = button.dataset.inactiveReason;
            const inactiveDate = button.dataset.inactiveDate;
            const mutasi = button.dataset.mutasi;
            const department = button.dataset.department;
            const division = button.dataset.division;
            const microsoftId = button.dataset.microsoftId;
            
            console.log('Data dari tombol edit:', {
                userId: userId,
                nama: nama,
                email: email,
                nip: nip,
                roleId: roleId,
                isActive: isActive,
                unitKerja: unitKerja,
                unitId: unitId,
                inactiveReason: inactiveReason,
                inactiveDate: inactiveDate,
                mutasi: mutasi,
                department: department,
                division: division,
                microsoftId: microsoftId
            });
            
            // Set nilai ke form
            $('#editUserId').val(userId);
            $('#edit_nama').val(nama);
            $('#edit_email').val(email);
            $('#edit_nip').val(nip);
            $('#edit_role').val(roleId);
            $('#edit_is_active').val(isActive);
            $('#edit_unit_kerja').val(unitKerja);
            $('#edit_inactive_reason').val(inactiveReason);
            $('#edit_inactive_date').val(inactiveDate);
            $('#edit_mutasi').val(mutasi);
            $('#edit_microsoft_id').val(microsoftId);
            
            // Trigger change event untuk status
            $('#edit_is_active').trigger('change');
            
            // Set unit_id
            const selectedUnitId = $('#edit_unit_kerja option:selected').data('id');
            $('#edit_unit_id').val(selectedUnitId);
            
            console.log('Selected unit ID:', selectedUnitId);
            
            // Pastikan dropdown division dan department dapat diedit jika ada unit yang dipilih
            if (selectedUnitId) {
                $('#edit_division').prop('disabled', false);
                $('#edit_department').prop('disabled', false);
                console.log('Division and Department dropdowns enabled');
            } else {
                $('#edit_division').prop('disabled', true);
                $('#edit_department').prop('disabled', true);
                console.log('Division and Department dropdowns disabled');
            }
            
            // Load division dan department berdasarkan unit
            if (selectedUnitId) {
                // Aktifkan dropdown division dan department
                $('#edit_division').prop('disabled', false);
                $('#edit_department').prop('disabled', false);
                
                // Load data division berdasarkan unit
                $.ajax({
                    url: '<?= base_url('administrator/getDivisionsByUnit'); ?>',
                    type: 'GET',
                    data: { unit_id: selectedUnitId },
                    dataType: 'json',
                    success: function(response) {
                        console.log('Response dari getDivisionsByUnit:', response);
                        if (response.status === 'success') {
                            let options = '<option value="">Pilih Division</option>';
                            response.data.forEach(function(div) {
                                const selected = (String(div.division) === String(division)) ? 'selected' : '';
                                options += `<option value="${div.division}" data-id="${div.id}" ${selected}>${div.division}</option>`;
                            });
                            $('#edit_division').html(options).prop('disabled', false);
                            console.log('Division dropdown populated and enabled');
                        } else {
                            $('#edit_division').html('<option value="">Pilih Division</option>').prop('disabled', true);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading divisions:', error);
                        $('#edit_division').html('<option value="">Error loading divisions</option>').prop('disabled', true);
                    }
                });
                
                // Load data department berdasarkan unit
                $.ajax({
                    url: '<?= base_url('administrator/getDepartmentsByUnit'); ?>',
                    type: 'GET',
                    data: { unit_id: selectedUnitId },
                    dataType: 'json',
                    success: function(response) {
                        console.log('Response dari getDepartmentsByUnit:', response);
                        if (response.status === 'success') {
                            let options = '<option value="">Pilih Department</option>';
                            response.data.forEach(function(dept) {
                                const selected = (String(dept.department) === String(department)) ? 'selected' : '';
                                options += `<option value="${dept.department}" data-id="${dept.id}" ${selected}>${dept.department}</option>`;
                            });
                            $('#edit_department').html(options).prop('disabled', false);
                            console.log('Department dropdown populated and enabled');
                        } else {
                            $('#edit_department').html('<option value="">Pilih Department</option>').prop('disabled', true);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading departments:', error);
                        $('#edit_department').html('<option value="">Error loading departments</option>').prop('disabled', true);
                    }
                });
            }
        });

        // Clear form when add user modal is opened
        $('#addUsersModal').on('show.bs.modal', function() {
            $('#addUserForm')[0].reset();
            $('#add_division').html('<option value="">Pilih Division</option>').prop('disabled', true);
            $('#add_department').html('<option value="">Pilih Department</option>').prop('disabled', true);
            $('#add_unit_id').val('');
        });



        // Submit form add user
        $('#addUserForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            $.ajax({
                url: '<?= base_url('administrator/addUser'); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.status === 'success') {
                        $('#addUsersModal').modal('hide');
                        alert('User berhasil ditambahkan');
                        location.reload();
                    } else {
                        alert(response.message || 'Terjadi kesalahan');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat menambahkan user');
                }
            });
        });

        // Submit form edit user
        $('#editUserForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            $.ajax({
                url: '<?= base_url('administrator/updateUser'); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.status === 'success') {
                        $('#editUserModal').modal('hide');
                        alert('User berhasil diperbarui');
                        location.reload();
                    } else {
                        alert(response.message || 'Terjadi kesalahan');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat memperbarui user');
                }
            });
        });
    });
</script>
